<?php

namespace app\api\controller\v1;

use app\api\model\File;
use think\facade\Request;
use think\facade\Db;
use think\facade\Env;
use think\file\UploadedFile;

class Upload
{
    // 允许上传的文件类型
    private $allowTypes = [
        'image' => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
        'video' => ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'],
        'document' => ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'],
        'audio' => ['mp3', 'wav', 'ogg', 'aac'],
        'archive' => ['zip', 'rar', '7z', 'tar', 'gz']
    ];
    
    // 配置信息
    private $config = [
        'development' => [
            'base_path' => 'D:/upload/',
            'base_url' => 'http://localhost/pics/'
        ],
        'production' => [
            'base_path' => '/home/<USER>/',
            'base_url' => 'https://your-domain.com/files/'
        ]
    ];

    public function uploadFile(): \think\response\Json
    {
        try {
            // 获取上传文件（确保总是返回数组）
            $files = request()->file();
            if (empty($files)) {
                return json(['code' => 400, 'msg' => '未选择上传文件']);
            }

            // 如果是单个文件上传，转换为数组
            if (!is_array($files)) {
                $files = [$files];
            }

            // 验证文件数量
            if (count($files) > 8) {
                return json(['code' => 400, 'msg' => '最多上传8个文件']);
            }

            // 获取上传用户ID
            $userId = request()->param('user_id', 0);
            
            // 获取存储类型和其他元数据
            $storageType = request()->param('storage_type', 0);
            $bucketName = request()->param('bucket_name', '');
            $deviceFingerprint = request()->param('device_fingerprint', '');
            
            // 获取环境配置
            $env = $this->getEnvironment();
            $basePath = $this->config[$env]['base_path'];
            $baseUrl = $this->config[$env]['base_url'];

            $result = [];
            
            // 开始事务
            Db::startTrans();
            
            foreach ($files as $key => $file) {
                // 检查文件是否有效
                if (!($file instanceof UploadedFile) || !$file->isValid()) {
                    continue; // 跳过无效文件
                }

                // 验证单个文件大小
                $fileSize = $file->getSize();
                if ($fileSize > 8 * 1024 * 1024) {
                    throw new \Exception('单个文件不能超过8M');
                }

                // 获取文件类型分类和扩展名
                $ext = $file->extension(); // 使用extension()方法更可靠
                $fileType = $this->getFileType($ext);
                $mimeType = $file->getMime();
                
                // 计算文件哈希值（先计算临时文件的哈希）
                $tempPath = $file->getPathname();
                $fileHash = md5_file($tempPath);
                
                // 检查是否已有相同哈希值的文件(避免重复存储)
                $existingFile = File::where([
                    'file_hash' => $fileHash,
                    'storage_type' => $storageType,
                    'bucket_name' => $bucketName
                ])->find();
                
                if ($existingFile) {
                    // 文件已存在，直接复用现有记录
                    $fileRecord = $existingFile;
                    $isDuplicate = true;
                    
                    // 构建响应数据
                    $resultItem = [
                        'file_id' => $fileRecord->file_id,
                        'original_name' => $file->getOriginalName(),
                        'save_path' => $fileRecord->file_path,
                        'file_type' => $fileType,
                        'mime_type' => $mimeType,
                        'size' => $fileSize,
                        'url' => $fileRecord->http_url,
                        'is_duplicate' => $isDuplicate
                    ];
                    
                    $result[] = $resultItem;
                    continue; // 跳过后续的文件保存步骤
                }

                // 生成存储路径
                $datePath = date('Ymd');
                $saveDir = $basePath . "{$datePath}/{$fileType}";

                // 创建目录（如果不存在）
                if (!is_dir($saveDir)) {
                    mkdir($saveDir, 0777, true);
                }

                // 生成随机文件名
                $newName = md5(uniqid()) . '.' . $ext;
                $relativePath = "upload/{$datePath}/{$fileType}/{$newName}";
                $httpUrl = $baseUrl . "{$datePath}/{$fileType}/{$newName}";

                // 移动文件到目标位置
                $fileInfo = $file->move($saveDir, $newName);
                if (!$fileInfo) {
                    throw new \Exception('文件保存失败');
                }
                
                // 准备写入file表的数据
                $fileData = [
                    'user_id' => $userId,
                    'original_name' => $file->getOriginalName(),
                    'store_name' => $newName,
                    'file_path' => $relativePath,
                    'file_size' => $fileSize,
                    'file_type' => $mimeType,
                    'file_extension' => $ext,
                    'file_hash' => $fileHash,
                    'hash_algorithm' => 'MD5',
                    'device_fingerprint' => $deviceFingerprint,
                    'storage_type' => $storageType,
                    'bucket_name' => $bucketName,
                    'http_url' => $httpUrl
                ];
                
                // 创建新的文件记录
                $fileRecord = File::create($fileData);
                $isDuplicate = false;
                
                // 构建响应数据
                $resultItem = [
                    'file_id' => $fileRecord->file_id,
                    'original_name' => $file->getOriginalName(),
                    'save_path' => $relativePath,
                    'file_type' => $fileType,
                    'mime_type' => $mimeType,
                    'size' => $fileSize,
                    'url' => $fileRecord->http_url,
                    'is_duplicate' => $isDuplicate
                ];
                
                $result[] = $resultItem;
            }

            if (empty($result)) {
                Db::rollback();
                return json(['code' => 400, 'msg' => '没有有效的文件被上传']);
            }
            
            // 提交事务
            Db::commit();

            return json([
                'code' => 200,
                'msg' => '上传成功',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            
            return json([
                'code' => 500,
                'msg' => '上传失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取文件类型
     * @param string $ext
     * @return string
     */
    private function getFileType($ext)
    {
        $ext = strtolower($ext);
        foreach ($this->allowTypes as $type => $exts) {
            if (in_array($ext, $exts)) {
                return $type;
            }
        }
        return 'other';
    }
    
    /**
     * 获取当前运行环境
     * @return string
     */
    private function getEnvironment()
    {
        // 通过操作系统类型判断环境
        $isWindows = strtoupper(substr(PHP_OS, 0, 3)) === 'WIN';
        
        // 也可以通过配置文件或环境变量判断
        // 例如: return Env::get('app_environment', $isWindows ? 'development' : 'production');
        
        return $isWindows ? 'development' : 'production';
    }
}