{"name": "topthink/think", "description": "the new thinkphp framework", "type": "project", "keywords": ["framework", "thinkphp", "ORM"], "homepage": "https://www.thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=8.0.0", "topthink/framework": "^8.0", "topthink/think-orm": "^3.0", "topthink/think-filesystem": "^2.0", "topthink/think-multi-app": "^1.0", "ext-openssl": "*", "firebase/php-jwt": "^6.10", "phpmailer/phpmailer": "^6.9", "ext-curl": "*", "openai-php/client": "^0.10.3", "symfony/http-client": "^7.2", "nyholm/psr7": "^1.8", "guzzlehttp/guzzle": "^7.9"}, "require-dev": {"symfony/var-dumper": ">=4.2", "topthink/think-trace": "^1.0"}, "autoload": {"psr-4": {"app\\": "app"}, "psr-0": {"": "extend/"}}, "config": {"preferred-install": "dist", "allow-plugins": {"php-http/discovery": true}}, "scripts": {"post-autoload-dump": ["@php think service:discover", "@php think vendor:publish"]}}