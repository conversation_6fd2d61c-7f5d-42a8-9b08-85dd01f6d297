# Resource 模块优化功能 API 文档

## 1. 获取资源列表（支持软删除数据）

### 接口信息
- **URL**: `GET /api/v1/resource/selectResourceAll`
- **功能**: 获取资源列表，支持查询软删除数据
- **优化**: 新增 `include_deleted` 参数支持

### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| include_deleted | boolean | 否 | 是否只返回软删除数据 | true |
| page | integer | 否 | 页码 | 1 |
| page_size | integer | 否 | 每页数量 | 10 |
| 其他参数 | - | 否 | 原有筛选参数 | - |

### 参数说明
- `include_deleted = true`: 只返回已软删除的数据
- `include_deleted = false` 或不传: 只返回未删除的数据（默认）

### 请求示例
```bash
# 获取已软删除的数据
GET /api/v1/resource/selectResourceAll?include_deleted=true&page=1&page_size=10

# 获取正常数据（默认）
GET /api/v1/resource/selectResourceAll?page=1&page_size=10
```

### 重要说明
前端传递 `include_deleted` 参数时，支持以下值：
- `true` (布尔值)
- `"true"` (字符串)
- `1` (数字)
- `"1"` (字符串)

任何一种格式都会被识别为要查询软删除数据。

### 数据格式说明
- 接口返回的是 ThinkPHP 的分页器对象，包含完整的分页信息
- 实际数据列表在 `data.data` 字段中
- 分页信息包括：`total`（总数）、`current_page`（当前页）、`per_page`（每页数量）等
- 这与其他模块保持一致的返回格式

### 响应格式
```json
{
  "code": 200,
  "msg": "获取资源列表成功",
  "data": {
    "list": [
      {
        "id": 1,
        "resource_name": "资源名称",
        "delete_time": "2024-01-02 10:30:00",
        "create_time": "2024-01-01 12:00:00",
        "author": {
          "id": 1,
          "username": "admin",
          "avatar": "avatar.jpg"
        },
        "tags": [
          {"name": "前端"},
          {"name": "Vue"}
        ],
        "downloadLinks": [
          {
            "id": 1,
            "method_name": "百度网盘",
            "download_link": "https://pan.baidu.com/xxx",
            "extraction_code": "1234"
          }
        ]
      }
    ],
    "pagination": {
      "total": 50,
      "current": 1,
      "page_size": 10
    }
  }
}
```

## 2. 批量删除资源

### 接口信息
- **URL**: `DELETE /api/v1/resource/delete`
- **功能**: 支持单个和批量删除，区分软删除和物理删除
- **优化**: 支持批量操作和详细的删除结果

### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| id | integer/array | 否 | 单个资源ID或ID数组 | 123 或 [123, 456] |
| ids | array | 否 | 资源ID数组（与id二选一） | [123, 456, 789] |
| real | boolean | 否 | 是否物理删除 | false |

### 参数说明
- `real = false` 或不传: 软删除（可恢复）
- `real = true`: 物理删除（不可恢复）
- `id` 和 `ids` 参数二选一，支持单个或批量删除

### 请求示例

#### 单个软删除
```bash
curl -X DELETE "/api/v1/resource/delete" \
  -H "Content-Type: application/json" \
  -d '{"id": 123}'
```

#### 批量软删除
```bash
curl -X DELETE "/api/v1/resource/delete" \
  -H "Content-Type: application/json" \
  -d '{"ids": [123, 456, 789]}'
```

#### 批量物理删除
```bash
curl -X DELETE "/api/v1/resource/delete" \
  -H "Content-Type: application/json" \
  -d '{"ids": [123, 456, 789], "real": true}'
```

### 响应格式

#### 全部成功
```json
{
  "code": 200,
  "msg": "批量软删除成功，共处理 3 个资源",
  "data": {
    "total": 3,
    "success": 3,
    "failed": 0,
    "results": [
      {"id": 123, "status": "success"},
      {"id": 456, "status": "success"},
      {"id": 789, "status": "success"}
    ]
  }
}
```

#### 部分成功
```json
{
  "code": 200,
  "msg": "批量软删除部分成功，成功 2 个，失败 1 个",
  "data": {
    "total": 3,
    "success": 2,
    "failed": 1,
    "failed_ids": [789],
    "results": [
      {"id": 123, "status": "success"},
      {"id": 456, "status": "success"},
      {"id": 789, "status": "failed", "message": "资源不存在"}
    ]
  }
}
```

#### 全部失败
```json
{
  "code": 500,
  "msg": "批量软删除失败，所有资源都处理失败",
  "data": {
    "total": 3,
    "success": 0,
    "failed": 3,
    "failed_ids": [123, 456, 789],
    "results": [
      {"id": 123, "status": "failed", "message": "资源不存在"},
      {"id": 456, "status": "failed", "message": "资源不存在"},
      {"id": 789, "status": "failed", "message": "资源不存在"}
    ]
  }
}
```

## JavaScript 调用示例（使用 axios）

### 获取资源列表
```javascript
// 获取已软删除的数据列表
const getDeletedResources = async (page = 1, pageSize = 10) => {
  const response = await axios.get('/api/v1/resource/selectResourceAll', {
    params: {
      include_deleted: true,
      page: page,
      page_size: pageSize
    }
  });
  return response.data;
};

// 获取正常数据列表
const getResources = async (page = 1, pageSize = 10) => {
  const response = await axios.get('/api/v1/resource/selectResourceAll', {
    params: {
      page: page,
      page_size: pageSize
    }
  });
  return response.data;
};
```

### 批量删除资源
```javascript
// 批量软删除
const batchSoftDelete = async (ids) => {
  const response = await axios.delete('/api/v1/resource/delete', {
    data: { ids: ids, real: false }
  });
  return response.data;
};

// 批量物理删除
const batchForceDelete = async (ids) => {
  const response = await axios.delete('/api/v1/resource/delete', {
    data: { ids: ids, real: true }
  });
  return response.data;
};

// 单个删除
const deleteResource = async (id, isPhysical = false) => {
  const response = await axios.delete('/api/v1/resource/delete', {
    data: { id: id, real: isPhysical }
  });
  return response.data;
};
```

### 使用示例
```javascript
// 获取已软删除的资源列表（回收站）
const deletedResources = await getDeletedResources(1, 20);
console.log('已删除资源数量:', deletedResources.data.total);
console.log('资源列表:', deletedResources.data.data);

// 获取正常资源列表
const normalResources = await getResources(1, 20);
console.log('正常资源数量:', normalResources.data.total);
console.log('当前页资源:', normalResources.data.data);

// 批量软删除资源
const deleteResult = await batchSoftDelete([123, 456, 789]);
if (deleteResult.code === 200) {
  console.log(`删除成功: ${deleteResult.data.success} 个`);
  if (deleteResult.data.failed > 0) {
    console.log(`删除失败: ${deleteResult.data.failed} 个`);
  }
}

// 单个物理删除
const forceDeleteResult = await deleteResource(123, true);
```

## 错误码说明
| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 500 | 服务器内部错误 |

## 调试接口

### 测试软删除查询
**URL**: `GET /api/v1/resource/testSoftDelete`

用于测试软删除功能是否正常工作，返回各种状态的数据统计。

```javascript
// 测试软删除功能
const testSoftDelete = async () => {
  const response = await axios.get('/api/v1/resource/testSoftDelete');
  return response.data;
};
```

## 注意事项
1. **软删除**: 默认删除方式，数据可通过恢复接口恢复
2. **物理删除**: 彻底删除数据，不可恢复，请谨慎使用
3. **批量操作**: 支持最大批量数量建议不超过100个
4. **事务处理**: 批量删除在事务中执行，确保数据一致性
5. **详细结果**: 返回每个资源的处理结果，便于前端处理
6. **参数格式**: `include_deleted` 支持多种格式：true, "true", 1, "1"
7. **调试日志**: 已添加详细日志，可查看应用日志排查问题
