# Category 模块优化功能 API 文档

## 1. 获取分类列表（支持软删除数据）

### 接口信息
- **URL**: `GET /api/v1/category/selectCategoryAll`
- **功能**: 获取分类列表，支持查询软删除数据
- **优化**: 新增 `include_deleted` 参数支持

### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| include_deleted | boolean | 否 | 是否只返回软删除数据 | true |
| id | integer | 否 | 分类ID精确查询 | 123 |
| name | string | 否 | 分类名称模糊查询 | "前端" |
| type | string | 否 | 分类类型精确查询 | "technology" |
| parent_id | integer | 否 | 父级ID（0=大类别，>0=标签） | 0 |
| user_id | integer | 否 | 创建用户ID | 1001 |
| description | string | 否 | 描述模糊查询 | "技术" |
| page_num | integer | 否 | 页码 | 1 |
| page_size | integer | 否 | 每页数量 | 20 |

### 参数说明
- `include_deleted = true`: 只返回已软删除的数据
- `include_deleted = false` 或不传: 只返回未删除的数据（默认）

### 重要说明
前端传递 `include_deleted` 参数时，支持以下值：
- `true` (布尔值)
- `"true"` (字符串)
- `1` (数字)
- `"1"` (字符串)

任何一种格式都会被识别为要查询软删除数据。

### 请求示例
```bash
# 获取已软删除的数据
GET /api/v1/category/selectCategoryAll?include_deleted=true&page_num=1&page_size=20

# 获取正常数据（默认）
GET /api/v1/category/selectCategoryAll?page_num=1&page_size=20
```

### 响应格式
```json
{
  "code": 200,
  "msg": "获取分类列表成功",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "前端技术",
        "type": "technology",
        "parent_id": 0,
        "description": "前端开发相关技术",
        "delete_time": "2024-01-02 10:30:00",
        "create_time": "2024-01-01 12:00:00",
        "update_time": "2024-01-01 12:00:00",
        "category_type_text": "大类别",
        "author": {
          "id": 1,
          "nickname": "管理员",
          "username": "admin",
          "avatar": "avatar.jpg"
        }
      }
    ],
    "pagination": {
      "total": 50,
      "current": 1,
      "page_size": 20
    }
  }
}
```

## 2. 批量删除分类

### 接口信息
- **URL**: `DELETE /api/v1/category/delete`
- **功能**: 支持单个和批量删除，区分软删除和物理删除
- **优化**: 支持批量操作和详细的删除结果

### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| id | integer/array | 否 | 单个分类ID或ID数组 | 123 或 [123, 456] |
| ids | array | 否 | 分类ID数组（与id二选一） | [123, 456, 789] |
| real | boolean | 否 | 是否物理删除 | false |

### 参数说明
- `real = false` 或不传: 软删除（可恢复）
- `real = true`: 物理删除（不可恢复）
- `id` 和 `ids` 参数二选一，支持单个或批量删除

### 请求示例

#### 单个软删除
```bash
curl -X DELETE "/api/v1/category/delete" \
  -H "Content-Type: application/json" \
  -d '{"id": 123}'
```

#### 批量软删除
```bash
curl -X DELETE "/api/v1/category/delete" \
  -H "Content-Type: application/json" \
  -d '{"ids": [123, 456, 789]}'
```

#### 批量物理删除
```bash
curl -X DELETE "/api/v1/category/delete" \
  -H "Content-Type: application/json" \
  -d '{"ids": [123, 456, 789], "real": true}'
```

### 响应格式

#### 全部成功
```json
{
  "code": 200,
  "msg": "批量软删除成功，共处理 3 个分类",
  "data": {
    "total": 3,
    "success": 3,
    "failed": 0,
    "results": [
      {"id": 123, "status": "success"},
      {"id": 456, "status": "success"},
      {"id": 789, "status": "success"}
    ]
  }
}
```

#### 部分成功
```json
{
  "code": 200,
  "msg": "批量软删除部分成功，成功 2 个，失败 1 个",
  "data": {
    "total": 3,
    "success": 2,
    "failed": 1,
    "failed_ids": [789],
    "results": [
      {"id": 123, "status": "success"},
      {"id": 456, "status": "success"},
      {"id": 789, "status": "failed", "message": "分类不存在"}
    ]
  }
}
```

## JavaScript 调用示例（使用 axios）

### 获取分类列表
```javascript
// 获取已软删除的分类列表
const getDeletedCategories = async (page = 1, pageSize = 20) => {
  const response = await axios.get('/api/v1/category/selectCategoryAll', {
    params: {
      include_deleted: true,
      page_num: page,
      page_size: pageSize
    }
  });
  return response.data;
};

// 获取正常分类列表
const getCategories = async (page = 1, pageSize = 20) => {
  const response = await axios.get('/api/v1/category/selectCategoryAll', {
    params: {
      page_num: page,
      page_size: pageSize
    }
  });
  return response.data;
};

// 获取大类别列表
const getMainCategories = async () => {
  const response = await axios.get('/api/v1/category/selectCategoryAll', {
    params: {
      parent_id: 0
    }
  });
  return response.data;
};

// 获取指定大类别下的标签
const getTagsByCategory = async (categoryId) => {
  const response = await axios.get('/api/v1/category/selectCategoryAll', {
    params: {
      parent_id: categoryId
    }
  });
  return response.data;
};
```

### 批量删除分类
```javascript
// 批量软删除
const batchSoftDeleteCategories = async (ids) => {
  const response = await axios.delete('/api/v1/category/delete', {
    data: { ids: ids, real: false }
  });
  return response.data;
};

// 批量物理删除
const batchForceDeleteCategories = async (ids) => {
  const response = await axios.delete('/api/v1/category/delete', {
    data: { ids: ids, real: true }
  });
  return response.data;
};

// 单个删除
const deleteCategory = async (id, isPhysical = false) => {
  const response = await axios.delete('/api/v1/category/delete', {
    data: { id: id, real: isPhysical }
  });
  return response.data;
};
```

### 使用示例
```javascript
// 获取已软删除的分类列表（回收站）
const deletedCategories = await getDeletedCategories(1, 20);

// 批量软删除分类
const deleteResult = await batchSoftDeleteCategories([123, 456, 789]);
if (deleteResult.code === 200) {
  console.log(`删除成功: ${deleteResult.data.success} 个`);
  if (deleteResult.data.failed > 0) {
    console.log(`删除失败: ${deleteResult.data.failed} 个`);
  }
}

// 获取前端技术分类下的所有标签
const frontendTags = await getTagsByCategory(5);
```

## 调试接口

### 测试软删除查询
**URL**: `GET /api/v1/category/testSoftDelete`

用于测试软删除功能是否正常工作，返回各种状态的数据统计。

```javascript
// 测试软删除功能
const testSoftDelete = async () => {
  const response = await axios.get('/api/v1/category/testSoftDelete');
  return response.data;
};
```

### 测试删除功能
**URL**: `GET /api/v1/category/testDelete?id=123`

用于测试删除功能，验证能否正确找到已软删除的分类进行物理删除。

```javascript
// 测试删除功能
const testDelete = async (id) => {
  const response = await axios.get(`/api/v1/category/testDelete?id=${id}`);
  return response.data;
};
```

## 错误码说明
| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 404 | 分类不存在 |
| 500 | 服务器内部错误 |

## 注意事项
1. **软删除**: 默认删除方式，数据可通过恢复接口恢复
2. **物理删除**: 彻底删除数据，不可恢复，请谨慎使用
3. **批量操作**: 支持最大批量数量建议不超过100个
4. **事务处理**: 批量删除在事务中执行，确保数据一致性
5. **详细结果**: 返回每个分类的处理结果，便于前端处理
6. **参数格式**: `include_deleted` 支持多种格式：true, "true", 1, "1"
7. **调试日志**: 已添加详细日志，可查看应用日志排查问题
8. **分类层级**: 支持大类别(parent_id=0)和标签(parent_id>0)的层级关系
9. **物理删除逻辑**: 物理删除可以操作已软删除的数据，使用 `withTrashed()` 查找记录
10. **删除区别**: 软删除只能操作未删除的记录，物理删除可以操作所有记录（包括已软删除的）
