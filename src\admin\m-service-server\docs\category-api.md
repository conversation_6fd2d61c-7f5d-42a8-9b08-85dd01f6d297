# 分类模块 API 调用文档

## 基础信息
- **基础路径**: `/api/v1/category`
- **功能**: 分类和标签管理，支持大类别和标签的层级关系
- **特性**: 软删除、动态查询、parent_id区分类型

## 分类类型说明
- **大类别**: `parent_id = 0`
- **标签**: `parent_id > 0`（指向大类别的ID）

## API 接口列表

### 1. 获取分类列表
**接口**: `GET /api/v1/category/selectCategoryAll`

**功能**: 获取分类列表，支持多条件动态查询和分页

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | integer | 否 | 分类ID精确查询 |
| name | string | 否 | 分类名称模糊查询 |
| type | string | 否 | 分类类型精确查询 |
| parent_id | integer | 否 | 父级ID（0=大类别，>0=标签） |
| user_id | integer | 否 | 创建用户ID |
| description | string | 否 | 描述模糊查询 |
| delete_status | string | 否 | 删除状态：only_deleted/with_deleted |
| page_num | integer | 否 | 页码，默认1 |
| page_size | integer | 否 | 每页数量，默认20 |

**响应格式**:
```json
{
  "code": 200,
  "msg": "获取分类列表成功",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "前端技术",
        "type": "technology",
        "parent_id": 0,
        "description": "前端开发相关技术",
        "create_time": "2024-01-01 12:00:00",
        "update_time": "2024-01-01 12:00:00",
        "delete_time": null,
        "category_type_text": "大类别",
        "author": {
          "id": 1,
          "nickname": "管理员",
          "username": "admin",
          "avatar": "avatar.jpg"
        }
      }
    ],
    "pagination": {
      "total": 50,
      "current": 1,
      "page_size": 20
    }
  }
}
```

### 2. 获取分类详情
**接口**: `GET /api/v1/category/selectCategoryById`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | integer | 是 | 分类ID |

**响应格式**: 包含父分类和子分类关联信息

### 3. 新增分类
**接口**: `POST /api/v1/category/add`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 是 | 分类名称 |
| type | string | 否 | 分类类型 |
| parent_id | integer | 否 | 父级ID，0为大类别，>0为标签 |
| description | string | 否 | 分类描述 |
| user_id | integer | 否 | 创建用户ID |
| status | integer | 否 | 状态，默认1 |
| sort_order | integer | 否 | 排序，默认0 |

**响应格式**:
```json
{
  "code": 200,
  "msg": "添加成功",
  "data": {
    "id": 123
  }
}
```

### 4. 更新分类
**接口**: `PUT /api/v1/category/update`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | integer | 是 | 分类ID |
| name | string | 否 | 分类名称 |
| slug | string | 否 | 分类别名 |
| type | string | 否 | 分类类型 |
| description | string | 否 | 分类描述 |
| sort_order | integer | 否 | 排序权重 |
| icon | string | 否 | 图标类名 |
| status | boolean | 否 | 状态（true/false） |
| parent_id | integer | 否 | 父级ID（0为大类别） |
| meta_title | string | 否 | SEO标题 |
| meta_keywords | string | 否 | SEO关键词 |
| meta_description | string | 否 | SEO描述 |
| user_id | integer | 否 | 用户ID |
| cover_image | string | 否 | 封面图片 |

**响应格式**:
```json
{
  "code": 200,
  "msg": "更新成功",
  "data": {
    "id": 1,
    "name": "生活2",
    "slug": "technology",
    "type": "product",
    "status": false,
    "update_time": "2025-07-29 19:23:09"
  }
}
```

### 5. 删除分类
**接口**: `DELETE /api/v1/category/delete`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | integer | 是 | 分类ID |
| real | boolean | 否 | 是否物理删除，默认false（软删除） |

**响应格式**:
```json
{
  "code": 200,
  "msg": "删除成功"
}
```

### 6. 恢复分类
**接口**: `POST /api/v1/category/restore`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | integer | 是 | 分类ID |

**响应格式**:
```json
{
  "code": 200,
  "msg": "恢复成功",
  "data": {
    "id": 123,
    "name": "分类名称",
    "delete_time": null
  }
}
```

### 7. 获取已删除分类列表
**接口**: `GET /api/v1/category/getDeletedCategories`

**请求参数**: 与获取分类列表相同，但只返回已删除的分类

**响应格式**: 与获取分类列表相同

## 使用示例

### JavaScript 调用示例

```javascript
// 1. 获取所有大类别
const getMainCategories = async () => {
  const response = await fetch('/api/v1/category/selectCategoryAll?parent_id=0');
  return await response.json();
};

// 2. 获取指定大类别下的标签
const getTagsByCategory = async (categoryId) => {
  const response = await fetch(`/api/v1/category/selectCategoryAll?parent_id=${categoryId}`);
  return await response.json();
};

// 3. 创建大类别
const createMainCategory = async (data) => {
  const response = await fetch('/api/v1/category/add', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      name: data.name,
      type: data.type,
      parent_id: 0, // 大类别
      description: data.description,
      user_id: data.user_id
    })
  });
  return await response.json();
};

// 4. 创建标签
const createTag = async (data) => {
  const response = await fetch('/api/v1/category/add', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      name: data.name,
      type: data.type,
      parent_id: data.parent_id, // 指向大类别ID
      description: data.description,
      user_id: data.user_id
    })
  });
  return await response.json();
};

// 5. 更新分类
const updateCategory = async (id, data) => {
  const response = await fetch('/api/v1/category/update', {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ id, ...data })
  });
  return await response.json();
};

// 6. 删除分类（软删除）
const deleteCategory = async (id) => {
  const response = await fetch('/api/v1/category/delete', {
    method: 'DELETE',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ id, real: false })
  });
  return await response.json();
};

// 7. 物理删除分类
const forceDeleteCategory = async (id) => {
  const response = await fetch('/api/v1/category/delete', {
    method: 'DELETE',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ id, real: true })
  });
  return await response.json();
};

// 8. 恢复分类
const restoreCategory = async (id) => {
  const response = await fetch('/api/v1/category/restore', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ id })
  });
  return await response.json();
};
```

### 常用查询场景

```javascript
// 查询所有大类别
const mainCategories = await fetch('/api/v1/category/selectCategoryAll?parent_id=0');

// 查询指定大类别下的所有标签
const tags = await fetch('/api/v1/category/selectCategoryAll?parent_id=5');

// 模糊搜索分类名称
const searchResults = await fetch('/api/v1/category/selectCategoryAll?name=前端');

// 查询指定类型的分类
const techCategories = await fetch('/api/v1/category/selectCategoryAll?type=technology');

// 查询已删除的分类
const deletedCategories = await fetch('/api/v1/category/selectCategoryAll?delete_status=only_deleted');

// 分页查询
const pageData = await fetch('/api/v1/category/selectCategoryAll?page_num=1&page_size=10');
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 404 | 分类不存在 |
| 500 | 服务器内部错误 |

## 注意事项

1. **分类层级**: 只支持两级，大类别(parent_id=0)和标签(parent_id>0)
2. **软删除**: 默认使用软删除，可通过real参数控制物理删除
3. **动态查询**: 支持多字段组合查询，参数为空时不参与查询条件
4. **分页**: 默认每页20条记录，最大100条
5. **权限**: 建议添加用户权限验证
6. **关联查询**: 自动加载作者信息，支持父子分类关联
