<?php

// +----------------------------------------------------------------------
// | 日志设置
// +----------------------------------------------------------------------
return [
    // 默认日志记录通道
    'default' => 'file',
    // 日志记录级别
    'level' => [],
    // 日志类型记录的通道 ['error'=>'email',...]
    'type_channel' => [
        'error' => 'error', // error类型的日志使用error通道
        'sql'   => 'sql',   // sql类型的日志使用sql通道
    ],
    // 关闭全局日志写入
    'close' => false,
    // 全局日志处理 支持闭包
    'processor' => null,

    // 日志通道列表
    'channels' => [
        'file' => [
            // 日志记录方式
            'type' => 'File',
            // 日志保存目录
            'path' => '',
            // 单文件日志写入
            'single' => false,
            // 独立日志级别
            'apart_level' => [],
            // 最大日志文件数量
            'max_files' => 0,
            // 使用JSON格式记录
            'json' => true,
            // 日志处理
            'processor' => null,
            // 关闭通道日志写入
            'close' => false,
//            时间格式
            'time_format' => 'Y-m-d H:i:s',

            // 日志输出格式化
            'format' => '[%s][%s] %s',
            // 是否实时写入
            'realtime_write' => false,
        ],
        // error日志通道
        'error' => [
            // 日志记录方式
            'type' => 'File',
            // 日志保存目录
            'path' => '',
            // 单文件日志写入
            'single' => false,
            // 日志文件名
            'file' => 'error',
            // 最大日志文件数量
            'max_files' => 30,
            // 使用JSON格式记录
            'json' => true,
            // 时间格式
            'time_format' => 'Y-m-d H:i:s',
            // 日志输出格式化
            'format' => '[%s][%s] %s',
            // 是否实时写入
            'realtime_write' => true,
        ],
        // sql日志通道
        'sql' => [
            // 日志记录方式
            'type' => 'File',
            // 日志保存目录
            'path' => '',
            // 单文件日志写入
            'single' => false,
            // 日志文件名
            'file' => 'sql',
            // 最大日志文件数量
            'max_files' => 30,
            // 使用JSON格式记录
            'json' => true,
            // 时间格式
            'time_format' => 'Y-m-d H:i:s',
            // 日志输出格式化
            'format' => '[%s][%s] %s',
            // 是否实时写入
            'realtime_write' => true,
        ],
    ],

];
