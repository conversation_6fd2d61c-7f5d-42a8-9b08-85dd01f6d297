用户表
1. id: 用户的唯一标识符，系统自动生成，无需用户输入。
2. username: 用户名，用户登录时使用的昵称，需要保证在系统中的唯一性。
3. password: 用户密码的哈希值，存储时需经过加密处理，以保证安全性。
4. email: 用户的电子邮箱地址，用于接收通知和密码重置等操作，需要保证唯一性。
5. create_time: 用户注册的时间戳，系统自动生成，记录用户创建账户的时间。
6. update_time: 用户信息最后修改的时间戳，系统自动更新，记录用户信息最后被修改的时间。
7. last_login: 用户最后登录的时间戳，用于跟踪用户的活动，可以为空。
8. status: 用户账户的状态，可以是 'active'（激活）、'disabled'（禁用）。
9. phone_number: 用户的手机号码，用于双因素认证或紧急联系，可以为空。
10. ip_address: 用户的IP地址，用于记录用户登录和操作的来源，可以为空。
11. gender: 用户的性别，可以是 'male'（男）、'female'（女）或 'other'（其他），可以为空。
12. nickname: 用户的昵称，用于在社区或评论中显示，可以为空。