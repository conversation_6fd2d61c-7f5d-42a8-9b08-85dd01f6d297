-- 支付方式表
-- 支持传统支付方式和加密货币支付
-- 包含FontAwesome图标支持

CREATE TABLE `bl_payment_methods` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '支付方式ID',
  `name` varchar(100) NOT NULL COMMENT '支付方式名称',
  `code` varchar(50) NOT NULL COMMENT '支付方式代码，唯一标识',
  `type` tinyint(2) NOT NULL DEFAULT '1' COMMENT '支付类型：1=传统支付，2=加密货币，3=数字钱包',
  `icon` varchar(100) DEFAULT NULL COMMENT 'FontAwesome图标类名，如：fas fa-credit-card',
  `description` text COMMENT '支付方式描述',
  `config` json DEFAULT NULL COMMENT '支付配置信息（JSON格式）',
  `currency_code` varchar(10) DEFAULT 'CNY' COMMENT '货币代码：CNY,USD,BTC,ETH等',
  `currency_symbol` varchar(10) DEFAULT '¥' COMMENT '货币符号',
  `is_crypto` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为加密货币：0=否，1=是',
  `network` varchar(50) DEFAULT NULL COMMENT '区块链网络（仅加密货币）：ETH,BSC,TRX等',
  `contract_address` varchar(100) DEFAULT NULL COMMENT '合约地址（代币）',
  `decimals` tinyint(3) DEFAULT NULL COMMENT '小数位数（加密货币）',
  `min_amount` decimal(20,8) DEFAULT '0.00000000' COMMENT '最小支付金额',
  `max_amount` decimal(20,8) DEFAULT '999999999.99999999' COMMENT '最大支付金额',
  `fee_rate` decimal(8,4) DEFAULT '0.0000' COMMENT '手续费率（百分比）',
  `fixed_fee` decimal(20,8) DEFAULT '0.00000000' COMMENT '固定手续费',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=禁用，1=启用',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序权重，数字越大越靠前',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为默认支付方式：0=否，1=是',
  `supported_countries` json DEFAULT NULL COMMENT '支持的国家/地区代码列表',
  `api_endpoint` varchar(255) DEFAULT NULL COMMENT 'API接口地址',
  `webhook_url` varchar(255) DEFAULT NULL COMMENT '回调地址',
  `merchant_id` varchar(100) DEFAULT NULL COMMENT '商户ID',
  `app_id` varchar(100) DEFAULT NULL COMMENT '应用ID',
  `secret_key` varchar(255) DEFAULT NULL COMMENT '密钥（加密存储）',
  `public_key` text DEFAULT NULL COMMENT '公钥',
  `sandbox_mode` tinyint(1) NOT NULL DEFAULT '0' COMMENT '沙盒模式：0=生产环境，1=测试环境',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_time` datetime DEFAULT NULL COMMENT '删除时间（软删除）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_is_crypto` (`is_crypto`),
  KEY `idx_currency_code` (`currency_code`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_delete_time` (`delete_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付方式表';

-- 插入默认支付方式数据
INSERT INTO `bl_payment_methods` (`name`, `code`, `type`, `icon`, `description`, `currency_code`, `currency_symbol`, `is_crypto`, `min_amount`, `max_amount`, `status`, `sort_order`, `is_default`) VALUES
('支付宝', 'alipay', 1, 'fab fa-alipay', '支付宝在线支付', 'CNY', '¥', 0, 0.01, 50000.00, 1, 100, 1),
('微信支付', 'wechat', 1, 'fab fa-weixin', '微信在线支付', 'CNY', '¥', 0, 0.01, 50000.00, 1, 90, 0),
('银联支付', 'unionpay', 1, 'fas fa-credit-card', '银联卡支付', 'CNY', '¥', 0, 0.01, 100000.00, 1, 80, 0),
('PayPal', 'paypal', 1, 'fab fa-paypal', 'PayPal国际支付', 'USD', '$', 0, 0.01, 10000.00, 1, 70, 0),
('比特币', 'bitcoin', 2, 'fab fa-bitcoin', '比特币加密货币支付', 'BTC', '₿', 1, 0.00001000, 10.00000000, 1, 60, 0),
('以太坊', 'ethereum', 2, 'fab fa-ethereum', '以太坊加密货币支付', 'ETH', 'Ξ', 1, 0.00100000, 100.00000000, 1, 50, 0),
('USDT-TRC20', 'usdt_trc20', 2, 'fas fa-coins', 'USDT泰达币(TRC20网络)', 'USDT', '$', 1, 1.00000000, 50000.00000000, 1, 40, 0),
('USDT-ERC20', 'usdt_erc20', 2, 'fas fa-coins', 'USDT泰达币(ERC20网络)', 'USDT', '$', 1, 1.00000000, 50000.00000000, 1, 30, 0);

-- 更新加密货币的网络信息
UPDATE `bl_payment_methods` SET `network` = 'BTC', `decimals` = 8 WHERE `code` = 'bitcoin';
UPDATE `bl_payment_methods` SET `network` = 'ETH', `decimals` = 18 WHERE `code` = 'ethereum';
UPDATE `bl_payment_methods` SET `network` = 'TRX', `decimals` = 6, `contract_address` = 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t' WHERE `code` = 'usdt_trc20';
UPDATE `bl_payment_methods` SET `network` = 'ETH', `decimals` = 6, `contract_address` = '******************************************' WHERE `code` = 'usdt_erc20';
