<?php

namespace app\api\controller\v1;

use app\BaseController;
use app\api\services\PaymentMethodService;
use think\facade\Validate;
use think\response\Json;

class PaymentMethod extends BaseController
{
    /**
     * 获取支付方式列表
     * @return Json
     */
    public function getPaymentMethodList(): Json
    {
        $params = request()->param();
        
        // 分页参数
        $page = (int)($params['page'] ?? 1);
        $limit = (int)($params['limit'] ?? 10);
        $queryDeleted = $params['query_deleted'] ?? 'not_deleted';
        
        // 查询条件
        $conditions = [];
        $searchFields = ['id', 'name', 'code', 'description', 'type', 'status', 'is_crypto', 'currency_code', 'network', 'is_default', 'sandbox_mode'];
        foreach ($searchFields as $field) {
            if (isset($params[$field]) && $params[$field] !== '') {
                $conditions[$field] = $params[$field];
            }
        }
        
        // 金额范围查询
        $amountFields = ['min_amount_from', 'min_amount_to', 'max_amount_from', 'max_amount_to'];
        foreach ($amountFields as $field) {
            if (isset($params[$field]) && $params[$field] !== '') {
                $conditions[$field] = $params[$field];
            }
        }
        
        $result = PaymentMethodService::getPaymentMethodList($conditions, $page, $limit, $queryDeleted);
        
        return json($result);
    }
    
    /**
     * 获取支付方式详情
     * @return Json
     */
    public function getPaymentMethodById(): Json
    {
        $id = (int)request()->param('id');
        
        if (!$id) {
            return json([
                'code' => 400,
                'msg' => '支付方式ID不能为空'
            ]);
        }
        
        $result = PaymentMethodService::getPaymentMethodById($id);
        
        return json($result);
    }
    
    /**
     * 添加支付方式
     * @return Json
     */
    public function addPaymentMethod(): Json
    {
        $params = request()->param();
        
        // 验证规则
        $validate = Validate::rule([
            'name' => 'require|max:100',
            'code' => 'require|max:50|alphaNum',
            'type' => 'require|in:1,2,3',
            'currency_code' => 'max:10',
            'currency_symbol' => 'max:10',
            'is_crypto' => 'in:0,1',
            'network' => 'max:50',
            'contract_address' => 'max:100',
            'decimals' => 'between:0,18',
            'min_amount' => 'float|egt:0',
            'max_amount' => 'float|gt:0',
            'fee_rate' => 'float|between:0,100',
            'fixed_fee' => 'float|egt:0',
            'status' => 'in:0,1',
            'sort_order' => 'integer',
            'is_default' => 'in:0,1',
            'sandbox_mode' => 'in:0,1',
            'icon' => 'max:100',
            'description' => 'max:1000'
        ])->message([
            'name.require' => '支付方式名称不能为空',
            'name.max' => '支付方式名称不能超过100个字符',
            'code.require' => '支付方式代码不能为空',
            'code.max' => '支付方式代码不能超过50个字符',
            'code.alphaNum' => '支付方式代码只能包含字母和数字',
            'type.require' => '支付类型不能为空',
            'type.in' => '支付类型必须为1(传统支付)、2(加密货币)或3(数字钱包)',
            'min_amount.float' => '最小支付金额必须为数字',
            'min_amount.egt' => '最小支付金额不能小于0',
            'max_amount.float' => '最大支付金额必须为数字',
            'max_amount.gt' => '最大支付金额必须大于0',
            'fee_rate.between' => '手续费率必须在0-100之间',
            'decimals.between' => '小数位数必须在0-18之间'
        ]);
        
        if (!$validate->check($params)) {
            return json([
                'code' => 400,
                'msg' => $validate->getError()
            ]);
        }
        
        // 处理JSON字段
        if (isset($params['config']) && is_string($params['config'])) {
            $params['config'] = json_decode($params['config'], true);
        }
        if (isset($params['supported_countries']) && is_string($params['supported_countries'])) {
            $params['supported_countries'] = json_decode($params['supported_countries'], true);
        }
        
        $result = PaymentMethodService::addPaymentMethod($params);
        
        return json($result);
    }
    
    /**
     * 更新支付方式
     * @return Json
     */
    public function updatePaymentMethod(): Json
    {
        $id = (int)request()->param('id');
        $params = request()->param();
        
        if (!$id) {
            return json([
                'code' => 400,
                'msg' => '支付方式ID不能为空'
            ]);
        }
        
        // 验证规则（更新时字段可选）
        $validate = Validate::rule([
            'name' => 'max:100',
            'code' => 'max:50|alphaNum',
            'type' => 'in:1,2,3',
            'currency_code' => 'max:10',
            'currency_symbol' => 'max:10',
            'is_crypto' => 'in:0,1',
            'network' => 'max:50',
            'contract_address' => 'max:100',
            'decimals' => 'between:0,18',
            'min_amount' => 'float|egt:0',
            'max_amount' => 'float|gt:0',
            'fee_rate' => 'float|between:0,100',
            'fixed_fee' => 'float|egt:0',
            'status' => 'in:0,1',
            'sort_order' => 'integer',
            'is_default' => 'in:0,1',
            'sandbox_mode' => 'in:0,1',
            'icon' => 'max:100',
            'description' => 'max:1000'
        ])->message([
            'name.max' => '支付方式名称不能超过100个字符',
            'code.max' => '支付方式代码不能超过50个字符',
            'code.alphaNum' => '支付方式代码只能包含字母和数字',
            'type.in' => '支付类型必须为1(传统支付)、2(加密货币)或3(数字钱包)',
            'min_amount.float' => '最小支付金额必须为数字',
            'min_amount.egt' => '最小支付金额不能小于0',
            'max_amount.float' => '最大支付金额必须为数字',
            'max_amount.gt' => '最大支付金额必须大于0',
            'fee_rate.between' => '手续费率必须在0-100之间',
            'decimals.between' => '小数位数必须在0-18之间'
        ]);
        
        if (!$validate->check($params)) {
            return json([
                'code' => 400,
                'msg' => $validate->getError()
            ]);
        }
        
        // 移除ID字段，避免更新主键
        unset($params['id']);
        
        // 处理JSON字段
        if (isset($params['config']) && is_string($params['config'])) {
            $params['config'] = json_decode($params['config'], true);
        }
        if (isset($params['supported_countries']) && is_string($params['supported_countries'])) {
            $params['supported_countries'] = json_decode($params['supported_countries'], true);
        }
        
        $result = PaymentMethodService::updatePaymentMethod($id, $params);
        
        return json($result);
    }
    
    /**
     * 删除支付方式
     * @return Json
     */
    public function deletePaymentMethod(): Json
    {
        $id = (int)request()->param('id');
        $real = (bool)request()->param('real', false);
        
        if (!$id) {
            return json([
                'code' => 400,
                'msg' => '支付方式ID不能为空'
            ]);
        }
        
        $result = PaymentMethodService::deletePaymentMethod($id, $real);
        
        return json($result);
    }
    
    /**
     * 恢复已删除的支付方式
     * @return Json
     */
    public function restorePaymentMethod(): Json
    {
        $id = (int)request()->param('id');
        
        if (!$id) {
            return json([
                'code' => 400,
                'msg' => '支付方式ID不能为空'
            ]);
        }
        
        $result = PaymentMethodService::restorePaymentMethod($id);
        
        return json($result);
    }
    
    /**
     * 更新支付方式状态
     * @return Json
     */
    public function updatePaymentMethodStatus(): Json
    {
        $id = (int)request()->param('id');
        $status = (int)request()->param('status');
        
        if (!$id) {
            return json([
                'code' => 400,
                'msg' => '支付方式ID不能为空'
            ]);
        }
        
        if (!in_array($status, [0, 1])) {
            return json([
                'code' => 400,
                'msg' => '状态值必须为0(禁用)或1(启用)'
            ]);
        }
        
        $result = PaymentMethodService::updatePaymentMethodStatus($id, $status);
        
        return json($result);
    }
    
    /**
     * 设置默认支付方式
     * @return Json
     */
    public function setDefaultPaymentMethod(): Json
    {
        $id = (int)request()->param('id');
        
        if (!$id) {
            return json([
                'code' => 400,
                'msg' => '支付方式ID不能为空'
            ]);
        }
        
        $result = PaymentMethodService::setDefaultPaymentMethod($id);
        
        return json($result);
    }
    
    /**
     * 获取启用的支付方式列表（用于前端选择）
     * @return Json
     */
    public function getEnabledPaymentMethods(): Json
    {
        $params = request()->param();
        
        // 查询条件
        $conditions = [];
        $filterFields = ['type', 'is_crypto', 'currency_code'];
        foreach ($filterFields as $field) {
            if (isset($params[$field]) && $params[$field] !== '') {
                $conditions[$field] = $params[$field];
            }
        }
        
        $result = PaymentMethodService::getEnabledPaymentMethods($conditions);
        
        return json($result);
    }
}
