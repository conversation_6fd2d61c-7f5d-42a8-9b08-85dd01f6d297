<?php
// 支付方式模块路由配置示例
// 请将以下路由添加到你的路由文件中

use think\facade\Route;

// 支付方式管理路由组
Route::group('payment-method', function () {
    // 获取支付方式列表
    Route::get('list', 'PaymentMethod/getPaymentMethodList');
    
    // 获取支付方式详情
    Route::get('detail/:id', 'PaymentMethod/getPaymentMethodById');
    
    // 添加支付方式
    Route::post('add', 'PaymentMethod/addPaymentMethod');
    
    // 更新支付方式
    Route::put('update/:id', 'PaymentMethod/updatePaymentMethod');
    
    // 删除支付方式
    Route::delete('delete/:id', 'PaymentMethod/deletePaymentMethod');
    
    // 更新支付方式状态
    Route::put('status/:id', 'PaymentMethod/updatePaymentMethodStatus');
    
    // 设置默认支付方式
    Route::post('set-default/:id', 'PaymentMethod/setDefaultPaymentMethod');
    
    // 获取启用的支付方式（用于前端选择）
    Route::get('enabled', 'PaymentMethod/getEnabledPaymentMethods');
    
})->prefix('api/v1/');

// 或者使用完整路径的方式：
/*
Route::get('api/v1/payment-method/list', 'app\api\controller\v1\PaymentMethod@getPaymentMethodList');
Route::get('api/v1/payment-method/detail/:id', 'app\api\controller\v1\PaymentMethod@getPaymentMethodById');
Route::post('api/v1/payment-method/add', 'app\api\controller\v1\PaymentMethod@addPaymentMethod');
Route::put('api/v1/payment-method/update/:id', 'app\api\controller\v1\PaymentMethod@updatePaymentMethod');
Route::delete('api/v1/payment-method/delete/:id', 'app\api\controller\v1\PaymentMethod@deletePaymentMethod');
Route::put('api/v1/payment-method/status/:id', 'app\api\controller\v1\PaymentMethod@updatePaymentMethodStatus');
Route::post('api/v1/payment-method/set-default/:id', 'app\api\controller\v1\PaymentMethod@setDefaultPaymentMethod');
Route::get('api/v1/payment-method/enabled', 'app\api\controller\v1\PaymentMethod@getEnabledPaymentMethods');
*/
