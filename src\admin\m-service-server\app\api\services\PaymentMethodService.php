<?php

namespace app\api\services;

use app\api\model\PaymentMethod;
use think\facade\Db;

/**
 * 支付方式服务类
 */
class PaymentMethodService
{
    /**
     * 获取支付方式列表
     * @param array $conditions 查询条件
     * @param int $page 页码，默认1
     * @param int $limit 每页条数，默认10
     * @param string $queryDeleted 删除状态：only_deleted-只查询已删除，not_deleted-只查询未删除，null-查询所有
     * @return array
     */
    public static function getPaymentMethodList(array $conditions = [], int $page = 1, int $limit = 10, string $queryDeleted = 'not_deleted'): array
    {
        try {
            // 记录SQL开始时间
            $startTime = microtime(true);
            
            // 构建查询条件
            $query = PaymentMethod::alias('pm');
            
            // ID精确匹配
            if (isset($conditions['id']) && $conditions['id'] !== '') {
                $query->where('pm.id', '=', $conditions['id']);
            }
            
            // 模糊匹配字段
            foreach (['name', 'code', 'description'] as $field) {
                if (!empty($conditions[$field])) {
                    $query->where('pm.'.$field, 'like', '%' . $conditions[$field] . '%');
                }
            }
            
            // 精确匹配字段
            foreach (['type', 'status', 'is_crypto', 'currency_code', 'network', 'is_default', 'sandbox_mode'] as $field) {
                if (isset($conditions[$field]) && $conditions[$field] !== '') {
                    $query->where('pm.'.$field, '=', $conditions[$field]);
                }
            }
            
            // 金额范围查询
            if (!empty($conditions['min_amount_from'])) {
                $query->where('pm.min_amount', '>=', $conditions['min_amount_from']);
            }
            if (!empty($conditions['min_amount_to'])) {
                $query->where('pm.min_amount', '<=', $conditions['min_amount_to']);
            }
            if (!empty($conditions['max_amount_from'])) {
                $query->where('pm.max_amount', '>=', $conditions['max_amount_from']);
            }
            if (!empty($conditions['max_amount_to'])) {
                $query->where('pm.max_amount', '<=', $conditions['max_amount_to']);
            }
            
            // 根据删除状态筛选
            if ($queryDeleted === 'only_deleted') {
                $query->onlyTrashed();
            } else if ($queryDeleted === 'not_deleted') {
                // 默认不查询已删除的记录
            } else {
                $query->withTrashed();
            }
            
            // 排序
            $query->order('pm.sort_order', 'desc')
                  ->order('pm.create_time', 'desc');
            
            // 分页查询
            $result = $query->paginate([
                'page' => $page,
                'list_rows' => $limit
            ]);
            
            // 添加文本描述字段
            $list = $result->items();
            foreach ($list as &$item) {
                $item->append(['type_text', 'status_text', 'is_crypto_text', 'is_default_text', 'sandbox_mode_text']);
            }
            
            // 记录SQL执行时间
            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2);
            LogService::sql(Db::getLastSql(), [], $executionTime);
            
            // 返回标准格式的分页数据
            return [
                'code' => 200,
                'msg' => '获取支付方式列表成功',
                'data' => [
                    'list' => $list,
                    'pagination' => [
                        'total' => $result->total(),
                        'current' => $result->currentPage(),
                        'page_size' => $limit,
                        'pages' => $result->lastPage()
                    ]
                ]
            ];
        } catch (\Exception $e) {
            LogService::error($e);
            return [
                'code' => 500,
                'msg' => '获取支付方式列表失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 获取支付方式详情
     * @param int $id 支付方式ID
     * @return array
     */
    public static function getPaymentMethodById(int $id): array
    {
        try {
            $startTime = microtime(true);
            
            $paymentMethod = PaymentMethod::withTrashed()->find($id);
            
            if (!$paymentMethod) {
                LogService::log("获取支付方式详情失败，ID不存在：{$id}", [], 'warning');
                return ['code' => 404, 'msg' => '支付方式不存在', 'data' => null];
            }
            
            // 添加文本描述字段
            $paymentMethod->append(['type_text', 'status_text', 'is_crypto_text', 'is_default_text', 'sandbox_mode_text']);
            
            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2);
            LogService::sql(Db::getLastSql(), [], $executionTime);
            
            return [
                'code' => 200,
                'msg' => '获取支付方式详情成功',
                'data' => $paymentMethod
            ];
        } catch (\Exception $e) {
            LogService::error($e);
            return [
                'code' => 500,
                'msg' => '获取支付方式详情失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 添加支付方式
     * @param array $data 支付方式数据
     * @return array
     */
    public static function addPaymentMethod(array $data): array
    {
        try {
            $startTime = microtime(true);
            
            // 验证必填字段
            if (empty($data['name']) || empty($data['code'])) {
                return ['code' => 400, 'msg' => '支付方式名称和代码不能为空'];
            }
            
            // 检查代码是否已存在
            $exists = PaymentMethod::where('code', $data['code'])->find();
            if ($exists) {
                LogService::log("添加支付方式失败，代码已存在：{$data['code']}", [], 'warning');
                return ['code' => 400, 'msg' => '支付方式代码已存在'];
            }
            
            // 如果设置为默认支付方式，需要取消其他默认设置
            if (!empty($data['is_default']) && $data['is_default'] == 1) {
                PaymentMethod::where('is_default', 1)->update(['is_default' => 0]);
            }
            
            // 创建支付方式
            $paymentMethod = new PaymentMethod();
            $paymentMethod->save($data);
            
            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2);
            LogService::sql(Db::getLastSql(), [], $executionTime);
            LogService::log("添加支付方式成功，ID：{$paymentMethod->id}，名称：{$data['name']}");
            
            return [
                'code' => 200,
                'msg' => '添加成功',
                'data' => $paymentMethod
            ];
        } catch (\Exception $e) {
            LogService::error($e);
            return [
                'code' => 500,
                'msg' => '添加失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 更新支付方式
     * @param int $id 支付方式ID
     * @param array $data 更新数据
     * @return array
     */
    public static function updatePaymentMethod(int $id, array $data): array
    {
        try {
            $startTime = microtime(true);
            
            // 查询支付方式是否存在
            $paymentMethod = PaymentMethod::find($id);
            if (!$paymentMethod) {
                LogService::log("更新支付方式失败，ID不存在：{$id}", [], 'warning');
                return ['code' => 404, 'msg' => '支付方式不存在'];
            }
            
            // 如果更新代码，检查是否与其他记录冲突
            if (!empty($data['code']) && $data['code'] !== $paymentMethod->code) {
                $exists = PaymentMethod::where('code', $data['code'])->where('id', '<>', $id)->find();
                if ($exists) {
                    LogService::log("更新支付方式失败，代码已存在：{$data['code']}", [], 'warning');
                    return ['code' => 400, 'msg' => '支付方式代码已存在'];
                }
            }
            
            // 如果设置为默认支付方式，需要取消其他默认设置
            if (!empty($data['is_default']) && $data['is_default'] == 1) {
                PaymentMethod::where('is_default', 1)->where('id', '<>', $id)->update(['is_default' => 0]);
            }
            
            // 更新支付方式
            $paymentMethod->save($data);
            
            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2);
            LogService::sql(Db::getLastSql(), [], $executionTime);
            LogService::log("更新支付方式成功，ID：{$id}，名称：{$paymentMethod->name}");
            
            // 添加文本描述字段
            $paymentMethod->append(['type_text', 'status_text', 'is_crypto_text', 'is_default_text', 'sandbox_mode_text']);
            
            return [
                'code' => 200,
                'msg' => '更新成功',
                'data' => $paymentMethod
            ];
        } catch (\Exception $e) {
            LogService::error($e);
            return [
                'code' => 500,
                'msg' => '更新失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 删除支付方式
     * @param int $id 支付方式ID
     * @param bool $real 是否物理删除，默认为false(软删除)
     * @return array
     */
    public static function deletePaymentMethod(int $id, bool $real = false): array
    {
        try {
            $startTime = microtime(true);

            // 查询支付方式是否存在
            $paymentMethod = PaymentMethod::withTrashed()->find($id);
            if (!$paymentMethod) {
                LogService::log("删除支付方式失败，ID不存在：{$id}", [], 'warning');
                return ['code' => 404, 'msg' => '支付方式不存在'];
            }

            // 检查是否为默认支付方式
            if ($paymentMethod->is_default == 1) {
                LogService::log("删除支付方式失败，不能删除默认支付方式：{$id}", [], 'warning');
                return ['code' => 400, 'msg' => '不能删除默认支付方式，请先设置其他支付方式为默认'];
            }

            // TODO: 检查是否有相关的支付订单（根据业务需求添加）
            // $orderCount = Db::name('orders')->where('payment_method_id', $id)->count();
            // if ($orderCount > 0) {
            //     return ['code' => 400, 'msg' => "该支付方式已被 {$orderCount} 个订单使用，无法删除"];
            // }

            $paymentMethodName = $paymentMethod->getData('name');

            if ($real) {
                // 物理删除
                $paymentMethod->force()->delete();
                LogService::log("物理删除支付方式成功，ID：{$id}，名称：{$paymentMethodName}");
                $message = '支付方式已永久删除';
            } else {
                // 软删除
                $paymentMethod->delete();
                LogService::log("软删除支付方式成功，ID：{$id}，名称：{$paymentMethodName}");
                $message = '支付方式已删除';
            }

            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2);
            LogService::sql(Db::getLastSql(), [], $executionTime);

            return [
                'code' => 200,
                'msg' => $message
            ];
        } catch (\Exception $e) {
            LogService::error($e);
            return [
                'code' => 500,
                'msg' => '删除失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 恢复已删除的支付方式
     * @param int $id 支付方式ID
     * @return array
     */
    public static function restorePaymentMethod(int $id): array
    {
        try {
            $startTime = microtime(true);

            // 查询已删除的支付方式
            $paymentMethod = PaymentMethod::onlyTrashed()->find($id);
            if (!$paymentMethod) {
                LogService::log("恢复支付方式失败，ID不存在或未删除：{$id}", [], 'warning');
                return ['code' => 404, 'msg' => '支付方式不存在或未删除'];
            }

            // 检查代码是否与现有记录冲突
            $exists = PaymentMethod::where('code', $paymentMethod->code)->find();
            if ($exists) {
                LogService::log("恢复支付方式失败，代码已存在：{$paymentMethod->code}", [], 'warning');
                return ['code' => 400, 'msg' => '支付方式代码已存在，无法恢复'];
            }

            // 恢复支付方式
            $paymentMethod->restore();

            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2);
            LogService::sql(Db::getLastSql(), [], $executionTime);
            LogService::log("恢复支付方式成功，ID：{$id}，名称：{$paymentMethod->name}");

            return [
                'code' => 200,
                'msg' => '恢复成功',
                'data' => $paymentMethod
            ];
        } catch (\Exception $e) {
            LogService::error($e);
            return [
                'code' => 500,
                'msg' => '恢复失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 更新支付方式状态
     * @param int $id 支付方式ID
     * @param int $status 状态：0=禁用，1=启用
     * @return array
     */
    public static function updatePaymentMethodStatus(int $id, int $status): array
    {
        try {
            $startTime = microtime(true);

            // 查询支付方式是否存在
            $paymentMethod = PaymentMethod::find($id);
            if (!$paymentMethod) {
                LogService::log("更新支付方式状态失败，ID不存在：{$id}", [], 'warning');
                return ['code' => 404, 'msg' => '支付方式不存在'];
            }

            // 如果是禁用默认支付方式，需要检查
            if ($paymentMethod->is_default == 1 && $status == 0) {
                LogService::log("更新支付方式状态失败，不能禁用默认支付方式：{$id}", [], 'warning');
                return ['code' => 400, 'msg' => '不能禁用默认支付方式，请先设置其他支付方式为默认'];
            }

            // 更新状态
            $paymentMethod->status = $status;
            $paymentMethod->save();

            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2);
            LogService::sql(Db::getLastSql(), [], $executionTime);

            $statusText = $status == 1 ? '启用' : '禁用';
            LogService::log("更新支付方式状态成功，ID：{$id}，状态：{$statusText}");

            return [
                'code' => 200,
                'msg' => '状态更新成功',
                'data' => $paymentMethod
            ];
        } catch (\Exception $e) {
            LogService::error($e);
            return [
                'code' => 500,
                'msg' => '状态更新失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 设置默认支付方式
     * @param int $id 支付方式ID
     * @return array
     */
    public static function setDefaultPaymentMethod(int $id): array
    {
        try {
            $startTime = microtime(true);

            // 查询支付方式是否存在且启用
            $paymentMethod = PaymentMethod::where('id', $id)->where('status', 1)->find();
            if (!$paymentMethod) {
                LogService::log("设置默认支付方式失败，ID不存在或未启用：{$id}", [], 'warning');
                return ['code' => 404, 'msg' => '支付方式不存在或未启用'];
            }

            Db::startTrans();
            try {
                // 取消所有默认设置
                PaymentMethod::where('is_default', 1)->update(['is_default' => 0]);

                // 设置新的默认支付方式
                $paymentMethod->is_default = 1;
                $paymentMethod->save();

                Db::commit();

                $endTime = microtime(true);
                $executionTime = round(($endTime - $startTime) * 1000, 2);
                LogService::sql(Db::getLastSql(), [], $executionTime);
                LogService::log("设置默认支付方式成功，ID：{$id}，名称：{$paymentMethod->name}");

                return [
                    'code' => 200,
                    'msg' => '设置默认支付方式成功',
                    'data' => $paymentMethod
                ];
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            LogService::error($e);
            return [
                'code' => 500,
                'msg' => '设置默认支付方式失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 获取启用的支付方式列表（用于前端选择）
     * @param array $conditions 查询条件
     * @return array
     */
    public static function getEnabledPaymentMethods(array $conditions = []): array
    {
        try {
            $startTime = microtime(true);

            $query = PaymentMethod::where('status', 1);

            // 根据类型筛选
            if (!empty($conditions['type'])) {
                $query->where('type', $conditions['type']);
            }

            // 根据是否为加密货币筛选
            if (isset($conditions['is_crypto'])) {
                $query->where('is_crypto', $conditions['is_crypto']);
            }

            // 根据货币代码筛选
            if (!empty($conditions['currency_code'])) {
                $query->where('currency_code', $conditions['currency_code']);
            }

            $paymentMethods = $query->field('id,name,code,type,icon,currency_code,currency_symbol,is_crypto,min_amount,max_amount,fee_rate,fixed_fee,sort_order,is_default')
                                   ->order('sort_order', 'desc')
                                   ->order('is_default', 'desc')
                                   ->select();

            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2);
            LogService::sql(Db::getLastSql(), [], $executionTime);

            return [
                'code' => 200,
                'msg' => '获取启用的支付方式成功',
                'data' => $paymentMethods
            ];
        } catch (\Exception $e) {
            LogService::error($e);
            return [
                'code' => 500,
                'msg' => '获取启用的支付方式失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
}
