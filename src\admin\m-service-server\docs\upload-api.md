# 文件上传接口文档

## 📋 目录
- [基础信息](#基础信息)
- [接口详情](#接口详情)
- [请求参数](#请求参数)
- [响应格式](#响应格式)
- [使用示例](#使用示例)
- [错误处理](#错误处理)
- [注意事项](#注意事项)

## 基础信息
- **接口地址**: `POST /api/v1/upload/uploadFile`
- **功能**: 多文件上传，支持图片、视频、文档、音频、压缩包等多种格式
- **特性**: 
  - 支持单文件/多文件上传（最多8个文件）
  - 自动去重（基于文件哈希值）
  - 事务处理确保数据一致性
  - 支持多种存储方式

## 接口详情

### 请求信息
- **URL**: `POST /api/v1/upload/uploadFile`
- **Content-Type**: `multipart/form-data`
- **最大文件数**: 8个文件
- **单文件大小限制**: 8MB

### 支持的文件类型
| 类型 | 扩展名 | 说明 |
|------|--------|------|
| **图片** | jpg, jpeg, png, gif, bmp, webp | 常见图片格式 |
| **视频** | mp4, avi, mov, wmv, flv, mkv | 常见视频格式 |
| **文档** | pdf, doc, docx, xls, xlsx, ppt, pptx, txt | 办公文档 |
| **音频** | mp3, wav, ogg, aac | 音频文件 |
| **压缩包** | zip, rar, 7z, tar, gz | 压缩文件 |

## 请求参数

### Form Data 参数
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| **文件参数** |
| file | File | 是 | 上传的文件（单文件） | - |
| files[] | File[] | 是 | 上传的文件数组（多文件） | - |
| **可选参数** |
| user_id | integer | 否 | 上传用户ID | 1001 |
| storage_type | integer | 否 | 存储类型 | 0 |
| bucket_name | string | 否 | 存储桶名称 | my-bucket |
| device_fingerprint | string | 否 | 设备指纹 | abc123 |

### 存储类型说明
| 值 | 类型 | 说明 |
|----|------|------|
| 0 | 本地存储 | 默认值，文件存储在服务器本地 |
| 1 | 阿里云OSS | 阿里云对象存储 |
| 2 | 七牛云 | 七牛云存储 |
| 3 | 腾讯云COS | 腾讯云对象存储 |

## 响应格式

### 成功响应
```json
{
  "code": 200,
  "msg": "上传成功",
  "data": [
    {
      "file_id": 12345,
      "original_name": "example.jpg",
      "save_path": "upload/20240120/image/abc123def456.jpg",
      "file_type": "image",
      "mime_type": "image/jpeg",
      "size": 1024000,
      "url": "http://localhost/pics/20240120/image/abc123def456.jpg",
      "is_duplicate": false
    },
    {
      "file_id": 12346,
      "original_name": "document.pdf",
      "save_path": "upload/20240120/document/def456ghi789.pdf",
      "file_type": "document",
      "mime_type": "application/pdf",
      "size": 2048000,
      "url": "http://localhost/pics/20240120/document/def456ghi789.pdf",
      "is_duplicate": false
    }
  ]
}
```

### 响应字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| file_id | integer | 文件唯一ID |
| original_name | string | 原始文件名 |
| save_path | string | 服务器存储路径 |
| file_type | string | 文件类型分类 |
| mime_type | string | MIME类型 |
| size | integer | 文件大小（字节） |
| url | string | 文件访问URL |
| is_duplicate | boolean | 是否为重复文件 |

### 错误响应
```json
{
  "code": 400,
  "msg": "具体错误信息",
  "data": null
}
```

## 使用示例

### 1. 单文件上传（JavaScript）
```javascript
const uploadSingleFile = async (file, userId = 0) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('user_id', userId);
  formData.append('storage_type', 0); // 本地存储
  
  try {
    const response = await fetch('/api/v1/upload/uploadFile', {
      method: 'POST',
      body: formData
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      console.log('上传成功:', result.data[0]);
      return result.data[0];
    } else {
      throw new Error(result.msg);
    }
  } catch (error) {
    console.error('上传失败:', error.message);
    throw error;
  }
};

// 使用示例
const fileInput = document.getElementById('fileInput');
fileInput.addEventListener('change', async (event) => {
  const file = event.target.files[0];
  if (file) {
    try {
      const uploadResult = await uploadSingleFile(file, 1001);
      console.log('文件URL:', uploadResult.url);
    } catch (error) {
      alert('上传失败: ' + error.message);
    }
  }
});
```

### 2. 多文件上传（JavaScript）
```javascript
const uploadMultipleFiles = async (files, userId = 0) => {
  const formData = new FormData();
  
  // 添加多个文件
  Array.from(files).forEach((file, index) => {
    formData.append('files[]', file);
  });
  
  formData.append('user_id', userId);
  formData.append('storage_type', 0);
  
  try {
    const response = await fetch('/api/v1/upload/uploadFile', {
      method: 'POST',
      body: formData
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      console.log('批量上传成功:', result.data);
      return result.data;
    } else {
      throw new Error(result.msg);
    }
  } catch (error) {
    console.error('批量上传失败:', error.message);
    throw error;
  }
};

// 使用示例
const multiFileInput = document.getElementById('multiFileInput');
multiFileInput.addEventListener('change', async (event) => {
  const files = event.target.files;
  if (files.length > 0) {
    try {
      const uploadResults = await uploadMultipleFiles(files, 1001);
      uploadResults.forEach((result, index) => {
        console.log(`文件${index + 1} URL:`, result.url);
      });
    } catch (error) {
      alert('批量上传失败: ' + error.message);
    }
  }
});
```

### 3. 带进度条的上传（JavaScript）
```javascript
const uploadWithProgress = async (file, onProgress) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('user_id', 1001);
  
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    
    // 监听上传进度
    xhr.upload.addEventListener('progress', (event) => {
      if (event.lengthComputable) {
        const percentComplete = (event.loaded / event.total) * 100;
        onProgress(Math.round(percentComplete));
      }
    });
    
    // 监听完成事件
    xhr.addEventListener('load', () => {
      if (xhr.status === 200) {
        const result = JSON.parse(xhr.responseText);
        if (result.code === 200) {
          resolve(result.data[0]);
        } else {
          reject(new Error(result.msg));
        }
      } else {
        reject(new Error('上传失败'));
      }
    });
    
    // 监听错误事件
    xhr.addEventListener('error', () => {
      reject(new Error('网络错误'));
    });
    
    xhr.open('POST', '/api/v1/upload/uploadFile');
    xhr.send(formData);
  });
};

// 使用示例
const uploadWithProgressBar = async (file) => {
  const progressBar = document.getElementById('progressBar');
  
  try {
    const result = await uploadWithProgress(file, (progress) => {
      progressBar.style.width = progress + '%';
      progressBar.textContent = progress + '%';
    });
    
    console.log('上传完成:', result.url);
  } catch (error) {
    console.error('上传失败:', error.message);
  }
};
```

### 4. Vue.js 组件示例
```vue
<template>
  <div class="upload-component">
    <input 
      type="file" 
      ref="fileInput"
      @change="handleFileSelect"
      multiple
      accept="image/*,video/*,.pdf,.doc,.docx"
    >
    <button @click="uploadFiles" :disabled="!selectedFiles.length || uploading">
      {{ uploading ? '上传中...' : '上传文件' }}
    </button>
    
    <div v-if="uploadResults.length" class="upload-results">
      <h3>上传结果:</h3>
      <div v-for="result in uploadResults" :key="result.file_id" class="file-item">
        <img v-if="result.file_type === 'image'" :src="result.url" alt="预览" width="100">
        <div>
          <p>文件名: {{ result.original_name }}</p>
          <p>大小: {{ formatFileSize(result.size) }}</p>
          <p>URL: <a :href="result.url" target="_blank">{{ result.url }}</a></p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      selectedFiles: [],
      uploading: false,
      uploadResults: []
    }
  },
  methods: {
    handleFileSelect(event) {
      this.selectedFiles = Array.from(event.target.files);
    },
    
    async uploadFiles() {
      if (!this.selectedFiles.length) return;
      
      this.uploading = true;
      const formData = new FormData();
      
      this.selectedFiles.forEach(file => {
        formData.append('files[]', file);
      });
      
      formData.append('user_id', this.$store.state.user.id);
      
      try {
        const response = await this.$http.post('/api/v1/upload/uploadFile', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });
        
        if (response.data.code === 200) {
          this.uploadResults = response.data.data;
          this.$message.success('上传成功');
        } else {
          throw new Error(response.data.msg);
        }
      } catch (error) {
        this.$message.error('上传失败: ' + error.message);
      } finally {
        this.uploading = false;
      }
    },
    
    formatFileSize(bytes) {
      const units = ['B', 'KB', 'MB', 'GB'];
      let i = 0;
      while (bytes >= 1024 && i < units.length - 1) {
        bytes /= 1024;
        i++;
      }
      return Math.round(bytes * 100) / 100 + ' ' + units[i];
    }
  }
}
</script>
```

## 错误处理

### 常见错误码
| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查文件是否选择、文件数量是否超限 |
| 500 | 服务器内部错误 | 检查文件大小、格式是否符合要求 |

### 常见错误信息
```json
// 未选择文件
{
  "code": 400,
  "msg": "未选择上传文件",
  "data": null
}

// 文件数量超限
{
  "code": 400,
  "msg": "最多上传8个文件",
  "data": null
}

// 文件大小超限
{
  "code": 500,
  "msg": "上传失败: 单个文件不能超过8M",
  "data": null
}

// 文件保存失败
{
  "code": 500,
  "msg": "上传失败: 文件保存失败",
  "data": null
}
```

## 注意事项

### 1. 文件大小限制
- 单个文件最大 8MB
- 建议在前端进行文件大小预检查

### 2. 文件类型限制
- 只支持预定义的文件类型
- 不支持的文件类型会被归类为 "other"

### 3. 去重机制
- 基于文件MD5哈希值进行去重
- 相同内容的文件会复用已有记录
- `is_duplicate` 字段标识是否为重复文件

### 4. 存储路径
- 文件按日期和类型分目录存储
- 格式: `upload/YYYYMMDD/文件类型/随机文件名.扩展名`

### 5. 事务处理
- 所有文件上传在同一事务中处理
- 任何文件上传失败都会回滚整个操作

### 6. 环境配置
- 开发环境: Windows系统，本地存储
- 生产环境: Linux系统，可配置云存储

### 7. 安全建议
- 建议添加用户身份验证
- 对上传文件进行病毒扫描
- 限制特定用户的上传权限

---

**文档版本**: v1.0  
**最后更新**: 2024-01-20  
**维护者**: 开发团队
