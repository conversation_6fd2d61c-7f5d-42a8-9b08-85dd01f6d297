# 文章模块API调用指南（已修复标签ID冲突问题）

## 🔧 问题修复说明

### 修复的问题
- **主键冲突错误**: `SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'xxxxx' for key 'PRIMARY'`
- **原因**: 标签ID生成时可能产生重复，特别是在修改文章时
- **解决方案**: 实现了ID唯一性检查和重试机制

### 优化的功能
1. **ID唯一性保证**: 生成ID时自动检查重复并重试
2. **统一标签处理**: 新增和更新使用相同的标签处理逻辑
3. **错误处理增强**: 更好的异常处理和日志记录
4. **性能优化**: 限制重试次数，避免无限循环

## 📋 API调用方式

### 1. 新增文章
```javascript
// 新增文章 - 标签ID自动生成且保证唯一
const createArticle = async (articleData) => {
  const response = await fetch('/api/v1/article/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      title: "Vue.js 3.0 新特性详解",
      content: "文章内容...",
      summary: "文章摘要",
      author_id: 1001,
      category_id: 5,
      status: 1,
      is_recommend: 1,
      tags: [
        { category_id: 10 },  // 前端技术
        { category_id: 11 },  // JavaScript
        { category_id: 12 }   // Vue.js
      ]
    })
  });
  return await response.json();
};
```

### 2. 更新文章
```javascript
// 更新文章 - 标签会先删除旧的，再创建新的，ID保证唯一
const updateArticle = async (articleId, updateData) => {
  const response = await fetch('/api/v1/article/update', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      id: articleId,
      title: "Vue.js 3.0 完整指南（更新版）",
      content: "更新后的内容...",
      tags: [
        { category_id: 10 },  // 保留的标签
        { category_id: 15 },  // 新增的标签
        { category_id: 20 }   // 新增的标签
      ]
    })
  });
  return await response.json();
};
```

### 3. 获取文章列表
```javascript
// 获取文章列表 - 包含标签信息
const getArticleList = async (params = {}) => {
  const queryString = new URLSearchParams({
    page: params.page || 1,
    page_size: params.page_size || 10,
    category_id: params.category_id || '',
    title: params.title || '',
    author_id: params.author_id || '',
    sort: params.sort || false,
    order: params.order || 'desc'
  }).toString();
  
  const response = await fetch(`/api/v1/article/selectArticleAll?${queryString}`);
  return await response.json();
};
```

### 4. 获取文章详情
```javascript
// 获取文章详情 - 包含完整的标签信息
const getArticleDetail = async (articleId) => {
  const response = await fetch(`/api/v1/article/selectArticleById?id=${articleId}`);
  return await response.json();
};
```

## 🔄 标签处理逻辑说明

### 新增文章时的标签处理
1. 验证标签数据格式
2. 为每个标签生成唯一ID
3. 检查ID是否重复，重复则重新生成
4. 批量插入标签关联记录
5. 在事务中执行，确保数据一致性

### 更新文章时的标签处理
1. 先删除该文章的所有旧标签关联
2. 为新标签生成唯一ID
3. 检查ID是否重复，重复则重新生成
4. 批量插入新的标签关联记录
5. 在事务中执行，确保数据一致性

### ID生成策略
```php
// 主要策略：5位随机数字（不以0开头）
$tagId = NumUtil::generateNumberCode(); // 生成如：12345

// 重复检查：最多重试10次
$attempts = 0;
while (Db::name('article_tag')->where('id', $tagId)->find() && $attempts < 10) {
    $tagId = NumUtil::generateNumberCode();
    $attempts++;
}

// 备用策略：时间戳+随机数（确保唯一性）
if ($attempts >= 10) {
    $tagId = time() . mt_rand(10, 99); // 生成如：172345678912
}
```

## 📊 响应数据格式

### 成功响应
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "article_id": 12345,
    // 或其他相关数据
  }
}
```

### 错误响应
```json
{
  "code": 500,
  "msg": "具体错误信息",
  "data": null
}
```

## ⚠️ 注意事项

### 1. 标签数据格式
```javascript
// 正确格式
tags: [
  { category_id: 10 },
  { category_id: 11 }
]

// 错误格式
tags: [10, 11]  // ❌ 不支持
tags: ["tag1", "tag2"]  // ❌ 不支持
```

### 2. 事务处理
- 文章创建和标签关联在同一事务中
- 如果任何步骤失败，整个操作会回滚
- 确保数据一致性

### 3. 性能考虑
- ID重复检查最多重试10次
- 使用批量插入提高性能
- 合理使用索引优化查询

### 4. 错误处理
- 所有操作都有完整的异常处理
- 详细的错误日志记录
- 用户友好的错误信息

## 🚀 使用示例

### 完整的文章管理流程
```javascript
class ArticleManager {
  // 创建文章
  async createArticle(data) {
    try {
      const result = await createArticle(data);
      if (result.code === 200) {
        console.log('文章创建成功:', result.data.article_id);
        return result.data.article_id;
      } else {
        throw new Error(result.msg);
      }
    } catch (error) {
      console.error('创建文章失败:', error.message);
      throw error;
    }
  }
  
  // 更新文章
  async updateArticle(id, data) {
    try {
      const result = await updateArticle(id, data);
      if (result.code === 200) {
        console.log('文章更新成功');
        return result.data;
      } else {
        throw new Error(result.msg);
      }
    } catch (error) {
      console.error('更新文章失败:', error.message);
      throw error;
    }
  }
  
  // 获取文章列表
  async getArticles(filters = {}) {
    try {
      const result = await getArticleList(filters);
      if (result.code === 200) {
        return {
          articles: result.data.list,
          pagination: result.data.pagination
        };
      } else {
        throw new Error(result.msg);
      }
    } catch (error) {
      console.error('获取文章列表失败:', error.message);
      throw error;
    }
  }
}

// 使用示例
const articleManager = new ArticleManager();

// 创建文章
const articleId = await articleManager.createArticle({
  title: "新文章标题",
  content: "文章内容",
  author_id: 1001,
  category_id: 5,
  tags: [
    { category_id: 10 },
    { category_id: 11 }
  ]
});

// 更新文章
await articleManager.updateArticle(articleId, {
  title: "更新后的标题",
  tags: [
    { category_id: 10 },
    { category_id: 12 },
    { category_id: 13 }
  ]
});

// 获取文章列表
const { articles, pagination } = await articleManager.getArticles({
  page: 1,
  page_size: 10,
  category_id: 5
});
```

---

**修复版本**: v1.1  
**修复日期**: 2024-01-20  
**主要改进**: 解决标签ID冲突问题，优化标签处理逻辑
