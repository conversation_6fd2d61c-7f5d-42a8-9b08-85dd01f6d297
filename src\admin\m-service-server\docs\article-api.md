# 文章模块 API 接口文档

## 📋 目录
- [基础信息](#基础信息)
- [通用说明](#通用说明)
- [API接口列表](#api接口列表)
  - [1. AI摘要生成](#1-ai摘要生成)
  - [2. 文章列表查询（管理端）](#2-文章列表查询管理端)
  - [3. 文章列表查询（客户端）](#3-文章列表查询客户端)
  - [4. 新增文章](#4-新增文章)
  - [5. 文章详情查询](#5-文章详情查询)
  - [6. 更新文章](#6-更新文章)
  - [7. 删除文章](#7-删除文章)
  - [8. 恢复文章](#8-恢复文章)
  - [9. 回收站文章列表](#9-回收站文章列表)
- [数据模型](#数据模型)
- [错误处理](#错误处理)

## 基础信息
- **服务名称**: 文章管理服务
- **基础路径**: `/api/v1/article`
- **协议**: HTTP/HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: 中间件验证（ParamFilter）

## 通用说明

### 请求头要求
```http
Content-Type: application/json
Accept: application/json
```

### 通用响应格式
所有接口都遵循统一的响应格式：
```json
{
  "code": 200,           // 状态码：200成功，其他为错误
  "msg": "操作成功",      // 响应消息
  "data": {}            // 响应数据，可能为对象、数组或null
}
```

### 分页格式
涉及列表查询的接口，分页信息格式如下：
```json
{
  "pagination": {
    "total": 100,        // 总记录数
    "current": 1,        // 当前页码
    "page_size": 10,     // 每页记录数
    "pages": 10          // 总页数
  }
}
```

## API接口列表

### 1. AI摘要生成

#### 接口信息
- **URL**: `POST /api/v1/article/getSummary`
- **功能**: 使用讯飞星火大模型API生成文章摘要
- **适用场景**: 编辑文章时自动生成摘要

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| prompt | string | 是 | 需要生成摘要的文章内容 | "这是一篇关于Vue.js的技术文章..." |

#### 请求示例
```bash
curl -X POST "http://localhost/api/v1/article/getSummary" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Vue.js是一个用于构建用户界面的渐进式JavaScript框架。它被设计为可以自底向上逐层应用..."
  }'
```

#### 响应示例
```json
{
  "code": 200,
  "msg": "生成摘要成功",
  "data": {
    "id": "chatcmpl-xxx",
    "object": "chat.completion",
    "created": 1704067200,
    "model": "Spark Lite",
    "choices": [
      {
        "index": 0,
        "message": {
          "role": "assistant",
          "content": "Vue.js是一个渐进式JavaScript框架，专注于构建用户界面，具有易学易用、灵活可扩展的特点。"
        },
        "finish_reason": "stop"
      }
    ],
    "usage": {
      "prompt_tokens": 50,
      "completion_tokens": 30,
      "total_tokens": 80
    }
  }
}
```

#### 错误响应
```json
{
  "code": 400,
  "msg": "摘要生成内容不能为空",
  "data": null
}
```

### 2. 文章列表查询（管理端）

#### 接口信息
- **URL**: `GET /api/v1/article/selectArticleAll`
- **功能**: 获取文章列表，支持复杂筛选条件和分页
- **适用场景**: 管理后台文章列表页面，支持全量数据查询

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例 | 备注 |
|--------|------|------|------|------|------|
| **基础筛选** |
| id | integer | 否 | 文章ID精确查询 | 12345 | 用于查询特定文章 |
| title | string | 否 | 文章标题模糊搜索 | "Vue教程" | 支持LIKE查询 |
| author_id | integer | 否 | 作者ID | 1001 | 查询指定作者的文章 |
| user_id | integer | 否 | 用户ID | 1001 | 与author_id同义，兼容性参数 |
| category_id | integer | 否 | 主分类ID | 5 | 查询指定分类下的文章 |
| **时间筛选** |
| create_time_start | string | 否 | 创建时间范围开始 | "2024-01-01 00:00:00" | 格式：Y-m-d H:i:s |
| create_time_end | string | 否 | 创建时间范围结束 | "2024-01-31 23:59:59" | 格式：Y-m-d H:i:s |
| create_time_gt | string | 否 | 创建时间大于 | "2024-01-15 12:00:00" | 不包含等于 |
| create_time_lt | string | 否 | 创建时间小于 | "2024-01-15 12:00:00" | 不包含等于 |
| update_time_start | string | 否 | 更新时间范围开始 | "2024-01-01 00:00:00" | 格式：Y-m-d H:i:s |
| update_time_end | string | 否 | 更新时间范围结束 | "2024-01-31 23:59:59" | 格式：Y-m-d H:i:s |
| update_time_gt | string | 否 | 更新时间大于 | "2024-01-15 12:00:00" | 不包含等于 |
| update_time_lt | string | 否 | 更新时间小于 | "2024-01-15 12:00:00" | 不包含等于 |
| publish_time_start | string | 否 | 发布时间范围开始 | "2024-01-01 00:00:00" | 格式：Y-m-d H:i:s |
| publish_time_end | string | 否 | 发布时间范围结束 | "2024-01-31 23:59:59" | 格式：Y-m-d H:i:s |
| publish_time_gt | string | 否 | 发布时间大于 | "2024-01-15 12:00:00" | 不包含等于 |
| publish_time_lt | string | 否 | 发布时间小于 | "2024-01-15 12:00:00" | 不包含等于 |
| **删除状态** |
| delete_status | string | 否 | 删除状态筛选 | "only_deleted" | only_deleted:仅已删除<br>with_deleted:包含已删除<br>不传:仅未删除 |
| **排序分页** |
| sort | boolean/string | 否 | 是否按sort字段排序 | true | true/1/"1"/"true":按sort排序<br>其他:按update_time排序 |
| order | string | 否 | 排序方向 | "desc" | asc:升序, desc:降序(默认) |
| page | integer | 否 | 页码 | 1 | 默认为1 |
| page_size | integer | 否 | 每页数量 | 20 | 默认为10 |

#### 请求示例
```bash
# 基础查询
curl "http://localhost/api/v1/article/selectArticleAll?page=1&page_size=10"

# 复杂筛选查询
curl "http://localhost/api/v1/article/selectArticleAll?title=Vue&author_id=1001&category_id=5&create_time_start=2024-01-01%2000:00:00&create_time_end=2024-01-31%2023:59:59&sort=true&order=desc&page=1&page_size=20"

# 查询已删除文章
curl "http://localhost/api/v1/article/selectArticleAll?delete_status=only_deleted"
```

#### 响应示例
```json
{
  "code": 200,
  "msg": "获取文章列表成功",
  "data": {
    "list": [
      {
        "id": 12345,
        "title": "Vue.js 3.0 完整教程",
        "content": "这是一篇详细介绍Vue.js 3.0新特性的教程文章...",
        "summary": "本文详细介绍了Vue.js 3.0的新特性，包括Composition API、性能优化等内容。",
        "author_id": 1001,
        "category_id": 5,
        "status": 1,
        "is_top": 0,
        "is_recommend": 1,
        "is_original": 1,
        "views": 1250,
        "sort": 100,
        "create_time": "2024-01-15 10:30:00",
        "update_time": "2024-01-16 14:20:00",
        "publish_time": "2024-01-15 12:00:00",
        "delete_time": null,
        "category": {
          "id": 5,
          "name": "前端技术",
          "slug": "frontend",
          "meta_title": "前端开发技术分享",
          "meta_keywords": "前端,JavaScript,Vue,React"
        },
        "author": {
          "id": 1001,
          "username": "techwriter",
          "nickname": "技术小编",
          "avatar": "https://example.com/avatar/1001.jpg",
          "signature": "专注前端技术分享",
          "roles": [
            {
              "id": 2,
              "name": "编辑",
              "description": "内容编辑",
              "show_weight": 80
            }
          ]
        },
        "tags": [
          {
            "name": "Vue.js"
          },
          {
            "name": "前端框架"
          },
          {
            "name": "JavaScript"
          }
        ],
        "favorites_count": 25,
        "likes_count": 89,
        "comments_count": 12
      }
    ],
    "pagination": {
      "total": 156,
      "current": 1,
      "page_size": 10,
      "pages": 16
    }
  }
}
```

#### 字段说明
| 字段名 | 类型 | 说明 | 备注 |
|--------|------|------|------|
| **文章基础信息** |
| id | integer | 文章唯一ID | 5位数字 |
| title | string | 文章标题 | 最大50字符 |
| content | string | 文章内容 | 富文本内容 |
| summary | string | 文章摘要 | 可为空 |
| author_id | integer | 作者ID | 关联users表 |
| category_id | integer | 主分类ID | 关联category表 |
| **状态字段** |
| status | integer | 发布状态 | 0:草稿 1:发布 2:待审核 3:已下架 |
| is_top | integer | 是否置顶 | 0:否 1:是 |
| is_recommend | integer | 是否推荐 | 0:否 1:是 |
| is_original | integer | 是否原创 | 0:否 1:是 |
| **统计字段** |
| views | integer | 浏览量 | 默认0 |
| sort | integer | 排序权重 | 数值越大越靠前 |
| favorites_count | integer | 收藏数 | 动态统计 |
| likes_count | integer | 点赞数 | 动态统计 |
| comments_count | integer | 评论数 | 动态统计 |
| **时间字段** |
| create_time | string | 创建时间 | Y-m-d H:i:s格式 |
| update_time | string | 更新时间 | Y-m-d H:i:s格式 |
| publish_time | string | 发布时间 | Y-m-d H:i:s格式，可为空 |
| delete_time | string/null | 删除时间 | 软删除时间，未删除为null |

### 3. 文章列表查询（客户端）

#### 接口信息
- **URL**: `GET /api/v1/article/getArticleList`
- **功能**: 前台客户端获取文章列表，参数经过安全过滤
- **适用场景**: 网站前台、移动端APP文章列表展示

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例 | 备注 |
|--------|------|------|------|------|------|
| id | integer | 否 | 文章ID | 12345 | 精确查询 |
| author_id | integer | 否 | 作者ID | 1001 | 查询指定作者文章 |
| category_id | integer | 否 | 分类ID | 5 | 查询指定分类文章 |
| title | string | 否 | 文章标题 | "Vue教程" | 模糊搜索 |
| keywords | string | 否 | 关键词 | "前端" | 用于搜索 |
| create_time | string | 否 | 创建时间 | "2024-01-01" | 日期筛选 |
| update_time | string | 否 | 更新时间 | "2024-01-01" | 日期筛选 |
| publish_time | string | 否 | 发布时间 | "2024-01-01" | 日期筛选 |
| page | integer | 否 | 页码 | 1 | 默认1 |
| page_size | integer | 否 | 每页数量 | 10 | 默认10 |

#### 请求示例
```bash
# 获取首页文章列表
curl "http://localhost/api/v1/article/getArticleList?page=1&page_size=10"

# 按分类查询
curl "http://localhost/api/v1/article/getArticleList?category_id=5&page=1&page_size=20"

# 搜索文章
curl "http://localhost/api/v1/article/getArticleList?title=Vue&keywords=前端"
```

#### 响应格式
与管理端接口相同，但只返回已发布的文章（status=1）

#### 安全说明
- 自动过滤敏感参数，只允许安全的查询条件
- 不支持删除状态查询，只返回正常文章
- 不支持复杂的时间范围查询，简化为单一时间点

### 4. 新增文章

#### 接口信息
- **URL**: `POST /api/v1/article/add`
- **功能**: 创建新文章，支持标签关联和事务处理
- **适用场景**: 管理后台新增文章，编辑器发布文章

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例 | 验证规则 |
|--------|------|------|------|------|----------|
| **基础信息** |
| id | integer | 否 | 文章ID | 12345 | 不传则自动生成5位数字ID |
| title | string | 是 | 文章标题 | "Vue.js 3.0 新特性详解" | 最大50字符，不能为空 |
| content | string | 是 | 文章内容 | "# 标题\n文章正文..." | 不能为空，支持Markdown |
| summary | string | 否 | 文章摘要 | "本文介绍Vue.js 3.0的新特性" | 可为空 |
| author_id | integer | 是 | 作者ID | 1001 | 必须存在的用户ID |
| category_id | integer | 否 | 主分类ID | 5 | 必须存在的分类ID |
| **状态设置** |
| status | integer | 否 | 发布状态 | 1 | 0:草稿 1:发布 2:待审核 3:已下架 |
| is_top | integer | 否 | 是否置顶 | 0 | 0:否 1:是 |
| is_recommend | integer | 否 | 是否推荐 | 1 | 0:否 1:是 |
| is_original | integer | 否 | 是否原创 | 1 | 0:否 1:是 |
| sort | integer | 否 | 排序权重 | 100 | 数值越大越靠前 |
| publish_time | string | 否 | 发布时间 | "2024-01-15 12:00:00" | Y-m-d H:i:s格式 |
| **标签关联** |
| tags | array | 是 | 文章标签 | 见下方示例 | 至少一个标签 |

#### 标签格式说明
```json
{
  "tags": [
    {
      "category_id": 10  // 标签分类ID，必须是category表中存在的ID
    },
    {
      "category_id": 11
    }
  ]
}
```

#### 请求示例
```bash
curl -X POST "http://localhost/api/v1/article/add" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Vue.js 3.0 Composition API 详解",
    "content": "# Vue.js 3.0 Composition API\n\nComposition API是Vue.js 3.0中最重要的新特性之一...",
    "summary": "详细介绍Vue.js 3.0 Composition API的使用方法和最佳实践",
    "author_id": 1001,
    "category_id": 5,
    "status": 1,
    "is_top": 0,
    "is_recommend": 1,
    "is_original": 1,
    "sort": 100,
    "publish_time": "2024-01-15 12:00:00",
    "tags": [
      {"category_id": 10},
      {"category_id": 11},
      {"category_id": 12}
    ]
  }'
```

#### 响应示例
```json
{
  "code": 200,
  "msg": "文章新增成功",
  "data": {
    "article_id": 67890
  }
}
```

#### 错误响应示例
```json
{
  "code": 422,
  "msg": "文章标题必须填写",
  "data": null
}
```

#### 业务逻辑说明
1. **ID生成**: 如果不传ID，系统自动生成5位数字ID
2. **事务处理**: 文章创建和标签关联在同一事务中，确保数据一致性
3. **标签处理**: 自动在article_tag中间表中创建关联记录
4. **时间戳**: 自动设置create_time和update_time
5. **验证机制**: 严格的参数验证，防止无效数据

### 5. 文章详情查询

#### 接口信息
- **URL**: `GET /api/v1/article/selectArticleById`
- **功能**: 根据ID获取文章完整详细信息，包含关联数据
- **适用场景**: 文章详情页、编辑页面数据加载

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| id | integer | 是 | 文章ID | 12345 |

#### 请求示例
```bash
# GET请求
curl "http://localhost/api/v1/article/selectArticleById?id=12345"

# POST请求
curl -X POST "http://localhost/api/v1/article/selectArticleById" \
  -H "Content-Type: application/json" \
  -d '{"id": 12345}'
```

#### 响应示例
```json
{
  "code": 200,
  "msg": "获取文章详情成功",
  "data": {
    "id": 12345,
    "title": "Vue.js 3.0 Composition API 详解",
    "content": "# Vue.js 3.0 Composition API\n\nComposition API是Vue.js 3.0中最重要的新特性之一，它提供了一种更灵活的方式来组织组件逻辑...",
    "summary": "详细介绍Vue.js 3.0 Composition API的使用方法和最佳实践",
    "author_id": 1001,
    "category_id": 5,
    "status": 1,
    "is_top": 0,
    "is_recommend": 1,
    "is_original": 1,
    "views": 2580,
    "sort": 100,
    "create_time": "2024-01-15 10:30:00",
    "update_time": "2024-01-16 14:20:00",
    "publish_time": "2024-01-15 12:00:00",
    "delete_time": null,
    "category": {
      "id": 5,
      "name": "前端技术",
      "slug": "frontend",
      "meta_title": "前端开发技术分享",
      "meta_keywords": "前端,JavaScript,Vue,React"
    },
    "author": {
      "id": 1001,
      "username": "techwriter",
      "nickname": "技术小编",
      "avatar": "https://example.com/avatar/1001.jpg",
      "signature": "专注前端技术分享"
    },
    "tags": [
      {
        "name": "Vue.js"
      },
      {
        "name": "Composition API"
      },
      {
        "name": "前端框架"
      }
    ],
    "comments": [
      {
        "id": 501,
        "content": "这篇文章写得很详细，对我帮助很大！",
        "create_time": "2024-01-16 09:15:00",
        "update_time": "2024-01-16 09:15:00",
        "delete_time": null
      },
      {
        "id": 502,
        "content": "Composition API确实是Vue 3的亮点功能",
        "create_time": "2024-01-16 11:30:00",
        "update_time": "2024-01-16 11:30:00",
        "delete_time": null
      }
    ],
    "favorites_count": 45,
    "likes_count": 128,
    "comments_count": 23
  }
}
```

#### 错误响应
```json
{
  "code": 404,
  "msg": "文章不存在",
  "data": null
}
```

#### 关联数据说明
- **category**: 文章主分类信息，包含SEO相关字段
- **author**: 作者基础信息，不包含敏感数据
- **tags**: 文章标签列表，仅包含标签名称
- **comments**: 文章评论列表，仅包含未删除的评论
- **统计数据**: 收藏数、点赞数、评论数为实时统计

### 6. 更新文章

#### 接口信息
- **URL**: `PUT /api/v1/article/update`
- **功能**: 更新文章信息，支持部分更新和标签重新关联
- **适用场景**: 编辑文章、修改文章状态、更新文章内容

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例 | 备注 |
|--------|------|------|------|------|------|
| **必填参数** |
| id | integer | 是 | 文章ID | 12345 | 要更新的文章ID |
| **可选更新字段** |
| title | string | 否 | 文章标题 | "Vue.js 3.0 完整指南" | 最大50字符 |
| content | string | 否 | 文章内容 | "更新后的文章内容..." | 支持Markdown |
| summary | string | 否 | 文章摘要 | "更新后的摘要" | 可为空 |
| category_id | integer | 否 | 主分类ID | 6 | 必须存在的分类ID |
| status | integer | 否 | 发布状态 | 1 | 0:草稿 1:发布 2:待审核 3:已下架 |
| is_top | integer | 否 | 是否置顶 | 1 | 0:否 1:是 |
| is_recommend | integer | 否 | 是否推荐 | 0 | 0:否 1:是 |
| is_original | integer | 否 | 是否原创 | 1 | 0:否 1:是 |
| sort | integer | 否 | 排序权重 | 150 | 数值越大越靠前 |
| publish_time | string | 否 | 发布时间 | "2024-01-20 15:00:00" | Y-m-d H:i:s格式 |
| tags | array | 否 | 文章标签 | 见下方示例 | 会完全替换现有标签 |

#### 请求示例
```bash
# 更新文章基础信息
curl -X PUT "http://localhost/api/v1/article/update" \
  -H "Content-Type: application/json" \
  -d '{
    "id": 12345,
    "title": "Vue.js 3.0 完整指南（更新版）",
    "summary": "这是一份更加完整和详细的Vue.js 3.0指南",
    "status": 1,
    "is_recommend": 1
  }'

# 更新文章内容和标签
curl -X PUT "http://localhost/api/v1/article/update" \
  -H "Content-Type: application/json" \
  -d '{
    "id": 12345,
    "content": "# Vue.js 3.0 完整指南\n\n这是更新后的内容...",
    "tags": [
      {"category_id": 10},
      {"category_id": 15},
      {"category_id": 20}
    ]
  }'

# 仅更新状态
curl -X PUT "http://localhost/api/v1/article/update" \
  -H "Content-Type: application/json" \
  -d '{
    "id": 12345,
    "status": 3
  }'
```

#### 响应示例
```json
{
  "code": 200,
  "msg": "更新成功",
  "data": {
    "id": 12345,
    "title": "Vue.js 3.0 完整指南（更新版）",
    "content": "# Vue.js 3.0 完整指南\n\n这是更新后的内容...",
    "summary": "这是一份更加完整和详细的Vue.js 3.0指南",
    "author_id": 1001,
    "category_id": 5,
    "status": 1,
    "is_top": 0,
    "is_recommend": 1,
    "is_original": 1,
    "views": 2580,
    "sort": 100,
    "create_time": "2024-01-15 10:30:00",
    "update_time": "2024-01-20 16:45:00",
    "publish_time": "2024-01-15 12:00:00",
    "delete_time": null
  }
}
```

#### 错误响应
```json
{
  "code": 404,
  "msg": "文章不存在",
  "data": null
}
```

#### 业务逻辑说明
1. **部分更新**: 只更新传入的字段，未传入的字段保持不变
2. **标签处理**: 如果传入tags参数，会先删除所有旧标签关联，再创建新的关联
3. **时间更新**: 自动更新update_time字段
4. **事务安全**: 标签更新在事务中进行，确保数据一致性
5. **权限检查**: 通常需要验证用户是否有权限修改该文章

### 7. 删除文章

#### 接口信息
- **URL**: `DELETE /api/v1/article/delete`
- **功能**: 删除文章，支持软删除和物理删除两种模式
- **适用场景**: 文章管理、回收站功能

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例 | 备注 |
|--------|------|------|------|------|------|
| id | integer | 是 | 文章ID | 12345 | 要删除的文章ID |
| real | boolean/string | 否 | 是否物理删除 | false | true:物理删除<br>false:软删除(默认) |

#### 请求示例
```bash
# 软删除（默认）
curl -X DELETE "http://localhost/api/v1/article/delete" \
  -H "Content-Type: application/json" \
  -d '{"id": 12345}'

# 物理删除
curl -X DELETE "http://localhost/api/v1/article/delete" \
  -H "Content-Type: application/json" \
  -d '{"id": 12345, "real": true}'
```

#### 响应示例
```json
{
  "code": 200,
  "msg": "删除成功"
}
```

#### 删除模式说明
- **软删除**: 设置delete_time字段，数据仍保留在数据库中，可以恢复
- **物理删除**: 从数据库中彻底删除记录，无法恢复

### 8. 恢复文章

#### 接口信息
- **URL**: `POST /api/v1/article/restore`
- **功能**: 恢复已软删除的文章
- **适用场景**: 回收站文章恢复

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| id | integer | 是 | 文章ID | 12345 |

#### 请求示例
```bash
curl -X POST "http://localhost/api/v1/article/restore" \
  -H "Content-Type: application/json" \
  -d '{"id": 12345}'
```

#### 响应示例
```json
{
  "code": 200,
  "msg": "恢复成功",
  "data": {
    "id": 12345,
    "title": "Vue.js 3.0 完整指南",
    "delete_time": null,
    // ... 其他文章信息
  }
}
```

### 9. 回收站文章列表

#### 接口信息
- **URL**: `GET /api/v1/article/getDeletedArticles`
- **功能**: 获取回收站中的已删除文章列表
- **适用场景**: 回收站管理页面

#### 请求参数
与文章列表查询接口相同，但会强制查询已删除的文章

#### 请求示例
```bash
curl "http://localhost/api/v1/article/getDeletedArticles?page=1&page_size=10"
```

#### 响应格式
与文章列表查询接口相同，但只返回已删除的文章（delete_time不为null）

## 数据模型

### 文章表结构 (article)
| 字段名 | 类型 | 长度 | 允许空 | 默认值 | 说明 |
|--------|------|------|--------|--------|------|
| id | int | 11 | 否 | - | 主键，5位数字ID |
| title | varchar | 255 | 否 | - | 文章标题 |
| content | longtext | - | 否 | - | 文章内容 |
| summary | text | - | 是 | null | 文章摘要 |
| author_id | int | 11 | 否 | - | 作者ID，关联users表 |
| category_id | int | 11 | 是 | null | 主分类ID，关联category表 |
| status | tinyint | 1 | 否 | 0 | 状态：0草稿 1发布 2待审核 3已下架 |
| is_top | tinyint | 1 | 否 | 0 | 是否置顶：0否 1是 |
| is_recommend | tinyint | 1 | 否 | 0 | 是否推荐：0否 1是 |
| is_original | tinyint | 1 | 否 | 1 | 是否原创：0否 1是 |
| views | int | 11 | 否 | 0 | 浏览量 |
| sort | int | 11 | 否 | 0 | 排序权重 |
| create_time | datetime | - | 是 | null | 创建时间 |
| update_time | datetime | - | 是 | null | 更新时间 |
| publish_time | datetime | - | 是 | null | 发布时间 |
| delete_time | datetime | - | 是 | null | 删除时间（软删除） |

### 文章标签关联表 (article_tag)
| 字段名 | 类型 | 长度 | 允许空 | 默认值 | 说明 |
|--------|------|------|--------|--------|------|
| id | int | 11 | 否 | - | 主键，5位数字ID |
| article_id | int | 11 | 否 | - | 文章ID |
| category_id | int | 11 | 否 | - | 标签分类ID |
| create_time | datetime | - | 是 | null | 创建时间 |

### 关联表说明
- **category**: 分类表，存储文章分类和标签
- **users**: 用户表，存储作者信息
- **favorites**: 收藏表，用于统计文章收藏数
- **likes**: 点赞表，用于统计文章点赞数
- **comments**: 评论表，用于统计文章评论数

## 错误处理

### 标准错误码
| 错误码 | HTTP状态码 | 说明 | 示例场景 |
|--------|------------|------|----------|
| 200 | 200 | 操作成功 | 正常请求处理成功 |
| 400 | 400 | 请求参数错误 | 缺少必填参数、参数格式错误 |
| 404 | 404 | 资源不存在 | 文章ID不存在、用户不存在 |
| 422 | 422 | 验证失败 | 数据验证不通过 |
| 500 | 500 | 服务器内部错误 | 数据库连接失败、系统异常 |

### 错误响应格式
```json
{
  "code": 400,
  "msg": "具体的错误信息",
  "data": null
}
```

### 常见错误示例
```json
// 参数验证失败
{
  "code": 422,
  "msg": "文章标题必须填写",
  "data": null
}

// 文章不存在
{
  "code": 404,
  "msg": "文章不存在",
  "data": null
}

// 系统错误
{
  "code": 500,
  "msg": "文章新增失败: 数据库连接超时",
  "data": null
}
```


## 快速开始

### 前端调用示例（JavaScript）

```javascript
// 1. 获取文章列表
async function getArticleList(params = {}) {
  const response = await fetch('/api/v1/article/selectArticleAll?' + new URLSearchParams(params));
  return await response.json();
}

// 2. 获取文章详情
async function getArticleDetail(id) {
  const response = await fetch(`/api/v1/article/selectArticleById?id=${id}`);
  return await response.json();
}

// 3. 创建文章
async function createArticle(articleData) {
  const response = await fetch('/api/v1/article/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(articleData)
  });
  return await response.json();
}

// 4. 更新文章
async function updateArticle(id, updateData) {
  const response = await fetch('/api/v1/article/update', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ id, ...updateData })
  });
  return await response.json();
}

// 5. 删除文章
async function deleteArticle(id, isPhysical = false) {
  const response = await fetch('/api/v1/article/delete', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ id, real: isPhysical })
  });
  return await response.json();
}
```

### 使用示例

```javascript
// 获取首页文章列表
const articleList = await getArticleList({
  page: 1,
  page_size: 10,
  status: 1,
  sort: true,
  order: 'desc'
});

// 创建新文章
const newArticle = await createArticle({
  title: "Vue.js 3.0 新特性介绍",
  content: "文章内容...",
  summary: "文章摘要",
  author_id: 1001,
  category_id: 5,
  status: 1,
  tags: [
    { category_id: 10 },
    { category_id: 11 }
  ]
});
```

