<?php

namespace app\api\middleware;

use think\facade\Log;
use utils\JWTUtil;
use utils\RedisUtil;
use utils\SecretUtil;

class Auth
{
    /**
     * @param $request
     * @param \Closure $next
     * @return mixed
     */
    function handle($request, \Closure $next): mixed
    {
        $accessToken = $request->header('accessToken');
        $refreshToken = request()->header('refreshToken');
        $currentId = request()->param('currentId');

//        请求路径
        $uri = request()->url();
//        验证是否存在token
        if (empty($refreshToken) || empty($accessToken)) {
            return json(['code' => 502, 'msg' => '请先登录再继续操作', 'status' => 'TOKEN_ERROR']);
        }
//        验证缓存中的token是否一致 是否异地登录

//        验证jwt是否有效
        $verifyToken = JWTUtil::verifyToken($refreshToken);
        if ($verifyToken['code'] != 200) {
////            写入日志
            Log::error(json_encode([
                'url' => $request->url(),
                'errorMsg' => $verifyToken['msg'],
                'IP' => $request->ip(),
            ]));
            return json(['code' => 5003, 'msg' => $verifyToken['msg'], 'status' => 'TOKEN_ERROR']);
        }
//        校验accessToken 解密自定义算法token
        $parseAccessToken = SecretUtil::parseAccessToken($accessToken);
//        解密失败时通常是伪造的
        $JWTUid = $verifyToken['data']['data']['id'];
       $redisJWT =RedisUtil::getString('lt_'.$JWTUid);
       if($redisJWT!=$refreshToken||empty($redisJWT)){
           return json(['code' => 5005, 'msg' => '登录失效', 'status' => 'TOKEN_ERROR']);
       }
        if (!$parseAccessToken) return json(['code' => 5003, 'msg' => '登录信息存在异常']);
        unset($request->version);
        $request->JWTUid = $JWTUid;
        return $next($request);
    }
}