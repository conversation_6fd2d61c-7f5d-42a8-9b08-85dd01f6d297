# 支付方式模块 API 接口文档（精简版）

## 📋 目录
- [基础信息](#基础信息)
- [通用说明](#通用说明)
- [API接口列表](#api接口列表)
  - [1. 获取支付方式列表](#1-获取支付方式列表)
  - [2. 获取支付方式详情](#2-获取支付方式详情)
  - [3. 添加支付方式](#3-添加支付方式)
  - [4. 更新支付方式](#4-更新支付方式)
  - [5. 删除支付方式](#5-删除支付方式)
  - [6. 更新支付方式状态](#6-更新支付方式状态)
  - [7. 设置默认支付方式](#7-设置默认支付方式)
  - [8. 获取启用的支付方式](#8-获取启用的支付方式)
- [数据模型](#数据模型)
- [错误处理](#错误处理)

## 基础信息
- **服务名称**: 支付方式管理服务
- **基础路径**: `/api/v1/paymentMethod`
- **协议**: HTTP/HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: 中间件验证

## 通用说明

### 请求头要求
```http
Content-Type: application/json
Accept: application/json
```

### 通用响应格式
所有接口都遵循统一的响应格式：
```json
{
  "code": 200,           // 状态码：200成功，其他为错误
  "msg": "操作成功",      // 响应消息
  "data": {}            // 响应数据，可能为对象、数组或null
}
```

## API接口列表

### 1. 获取支付方式列表

**接口地址**: `GET /api/v1/paymentMethod/selectPaymentMethodAll`

**请求参数**:
```javascript
// axios调用示例
const response = await axios.get('/api/v1/paymentMethod/selectPaymentMethodAll', {
  params: {
    page: 1,                    // 页码，默认1
    limit: 10,                  // 每页条数，默认10
    id: '',                     // 支付方式ID
    name: '',                   // 支付方式名称（模糊搜索）
    code: '',                   // 支付方式代码（模糊搜索）
    type: '',                   // 支付类型：1=传统支付，2=加密货币，3=数字钱包
    status: '',                 // 状态：0=禁用，1=启用
    is_crypto: '',              // 是否为加密货币：0=否，1=是
    currency_code: '',          // 货币代码
    network: '',                // 区块链网络
    is_default: ''              // 是否为默认：0=否，1=是
  }
});
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "获取支付方式列表成功",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "支付宝",
        "code": "alipay",
        "type": 1,
        "type_text": "传统支付",
        "icon": "fab fa-alipay",
        "currency_code": "CNY",
        "currency_symbol": "¥",
        "is_crypto": 0,
        "is_crypto_text": "否",
        "network": null,
        "contract_address": null,
        "status": 1,
        "status_text": "启用",
        "is_default": 1,
        "is_default_text": "是",
        "sort_order": 100,
        "create_time": "2024-01-01 12:00:00",
        "update_time": "2024-01-01 12:00:00"·
      }
    ],
    "pagination": {
      "total": 8,
      "current": 1,
      "page_size": 10,
      "pages": 1
    }
  }
}
```

### 2. 获取支付方式详情

**接口地址**: `GET /api/v1/paymentMethod/selectPaymentMethodById`

**请求参数**:
```javascript
// axios调用示例
const response = await axios.get('/api/v1/paymentMethod/selectPaymentMethodById', {
  params: { id: id }
});
```

### 3. 添加支付方式

**接口地址**: `POST /api/v1/paymentMethod/add`

**请求参数**:
```javascript
// axios调用示例
const response = await axios.post('/api/v1/paymentMethod/add', {
  name: '支付宝',                    // 必填：支付方式名称
  code: 'alipay',                   // 必填：支付方式代码（唯一）
  type: 1,                          // 必填：支付类型
  icon: 'fab fa-alipay',            // 可选：FontAwesome图标
  currency_code: 'CNY',             // 可选：货币代码
  currency_symbol: '¥',             // 可选：货币符号
  is_crypto: 0,                     // 可选：是否为加密货币
  network: '',                      // 可选：区块链网络
  contract_address: '',             // 可选：合约地址
  status: 1,                        // 可选：状态
  sort_order: 100,                  // 可选：排序权重
  is_default: 0                     // 可选：是否默认
});
```

### 4. 更新支付方式

**接口地址**: `POST /api/v1/paymentMethod/update`

**请求参数**:
```javascript
// axios调用示例
const response = await axios.post('/api/v1/paymentMethod/update', {
  id: id,  // 必填：支付方式ID
  name: '支付宝支付',
  status: 1,
  // 其他字段同添加接口，但都为可选
});
```

### 5. 删除支付方式

**接口地址**: `POST /api/v1/paymentMethod/delete`

**请求参数**:
```javascript
// 删除支付方式
const response = await axios.post('/api/v1/paymentMethod/delete', {
  id: id  // 必填：支付方式ID
});
```

### 6. 更新支付方式状态

**接口地址**: `POST /api/v1/paymentMethod/updateStatus`

**请求参数**:
```javascript
// axios调用示例
const response = await axios.post('/api/v1/paymentMethod/updateStatus', {
  id: id,    // 必填：支付方式ID
  status: 1  // 必填：0=禁用，1=启用
});
```

### 7. 设置默认支付方式

**接口地址**: `POST /api/v1/paymentMethod/setDefault`

**请求参数**:
```javascript
// axios调用示例
const response = await axios.post('/api/v1/paymentMethod/setDefault', {
  id: id  // 必填：支付方式ID
});
```

### 8. 获取启用的支付方式

**接口地址**: `GET /api/v1/paymentMethod/getEnabledList`

**请求参数**:
```javascript
// axios调用示例
const response = await axios.get('/api/v1/paymentMethod/getEnabledList', {
  params: {
    type: 1,              // 可选：支付类型筛选
    is_crypto: 0,         // 可选：是否为加密货币筛选
    currency_code: 'CNY'  // 可选：货币代码筛选
  }
});
```

## 数据模型

### PaymentMethod 支付方式模型
```typescript
interface PaymentMethod {
  id: number;                    // 支付方式ID
  name: string;                  // 支付方式名称
  code: string;                  // 支付方式代码
  type: number;                  // 支付类型：1=传统支付，2=加密货币，3=数字钱包
  type_text: string;             // 支付类型文本
  icon: string;                  // FontAwesome图标类名
  currency_code: string;         // 货币代码
  currency_symbol: string;       // 货币符号
  is_crypto: number;             // 是否为加密货币：0=否，1=是
  is_crypto_text: string;        // 是否为加密货币文本
  network: string;               // 区块链网络
  contract_address: string;      // 合约地址
  status: number;                // 状态：0=禁用，1=启用
  status_text: string;           // 状态文本
  sort_order: number;            // 排序权重
  is_default: number;            // 是否为默认：0=否，1=是
  is_default_text: string;       // 是否为默认文本
  create_time: string;           // 创建时间
  update_time: string;           // 更新时间
}
```

## 错误处理

### 常见错误码
- `400`: 请求参数错误
- `404`: 支付方式不存在
- `500`: 服务器内部错误

### 错误响应示例
```json
{
  "code": 400,
  "msg": "支付方式名称不能为空",
  "data": null
}
```

## 使用示例

### 完整的前端调用示例
```javascript
// 1. 获取支付方式列表
const getPaymentMethods = async () => {
  try {
    const response = await axios.get('/api/v1/paymentMethod/selectPaymentMethodAll', {
      params: { page: 1, limit: 10, status: 1 }
    });
    if (response.data.code === 200) {
      console.log('支付方式列表:', response.data.data.list);
    }
  } catch (error) {
    console.error('获取失败:', error);
  }
};

// 2. 添加新的支付方式
const addPaymentMethod = async () => {
  try {
    const response = await axios.post('/api/v1/paymentMethod/add', {
      name: 'USDT-TRC20',
      code: 'usdt_trc20',
      type: 2,
      icon: 'fas fa-coins',
      currency_code: 'USDT',
      currency_symbol: '$',
      is_crypto: 1,
      network: 'TRX',
      contract_address: 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t',
      status: 1
    });
    if (response.data.code === 200) {
      console.log('添加成功:', response.data.data);
    }
  } catch (error) {
    console.error('添加失败:', error);
  }
};

// 3. 获取启用的支付方式（用于支付选择）
const getEnabledMethods = async () => {
  try {
    const response = await axios.get('/api/v1/paymentMethod/getEnabledList');
    if (response.data.code === 200) {
      console.log('可用支付方式:', response.data.data);
    }
  } catch (error) {
    console.error('获取失败:', error);
  }
};
```
