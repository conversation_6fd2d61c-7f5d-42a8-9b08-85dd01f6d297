<?php

namespace app\api\controller\v1;

use app\api\services\FileService;
use app\BaseController;
use think\response\Json;

class File extends BaseController
{
    /**
     * 获取文件列表
     * @return Json
     */
    public function getFileList(): Json
    {
        $params = request()->param();
        $data = FileService::getFileList($params);
        return json(['code' => 200, 'data' => $data]);
    }

    /**
     * 获取文件详情
     * @return Json
     */
    public function getFileById(): Json
    {
        $fileId = request()->param('file_id');
        
        if (!$fileId || !is_numeric($fileId)) {
            return json(['code' => 400, 'message' => '无效的文件ID']);
        }

        $result = FileService::getFileById((int)$fileId);
        
        return json([
            'code' => $result['success'] ? 200 : 500,
            'data' => $result['data'] ?? null,
            'message' => $result['message']
        ]);
    }

    /**
     * 删除文件（软删除）
     * @return Json
     */
    public function deleteFile(): Json
    {
        $fileId = request()->param('file_id');
        
        if (!$fileId || !is_numeric($fileId)) {
            return json(['code' => 400, 'message' => '无效的文件ID']);
        }

        $result = FileService::deleteFile((int)$fileId);
        
        return json([
            'code' => $result['success'] ? 200 : 500,
            'message' => $result['message']
        ]);
    }

    /**
     * 恢复被删除的文件
     * @return Json
     */
    public function restoreFile(): Json
    {
        $fileId = request()->param('file_id');
        
        if (!$fileId || !is_numeric($fileId)) {
            return json(['code' => 400, 'message' => '无效的文件ID']);
        }

        $result = FileService::restoreFile((int)$fileId);
        
        return json([
            'code' => $result['success'] ? 200 : 500,
            'message' => $result['message']
        ]);
    }

    /**
     * 永久删除文件
     * @return Json
     */
    public function forceDeleteFile(): Json
    {
        $fileId = request()->param('file_id');
        $deletePhysicalFile = (bool)request()->param('delete_physical', false);
        
        if (!$fileId || !is_numeric($fileId)) {
            return json(['code' => 400, 'message' => '无效的文件ID']);
        }

        $result = FileService::forceDeleteFile((int)$fileId, $deletePhysicalFile);
        
        return json([
            'code' => $result['success'] ? 200 : 500,
            'message' => $result['message']
        ]);
    }

    /**
     * 批量删除文件
     * @return Json
     */
    public function batchDeleteFiles(): Json
    {
        $fileIds = request()->param('file_ids/a');  // /a 表示接收数组参数
        $isForce = (bool)request()->param('is_force', false);
        
        if (!$fileIds || !is_array($fileIds)) {
            return json(['code' => 400, 'message' => '请提供要删除的文件ID列表']);
        }

        $result = FileService::batchDeleteFiles($fileIds, $isForce);
        
        return json([
            'code' => $result['success'] ? 200 : 500,
            'message' => $result['message'],
            'data' => [
                'fail_ids' => $result['fail_ids'] ?? []
            ]
        ]);
    }

    /**
     * 获取文件统计信息
     * @return Json
     */
    public function getFileStats(): Json
    {
        $result = FileService::getFileStats();
        
        return json([
            'code' => $result['success'] ? 200 : 500,
            'data' => $result['data'] ?? null,
            'message' => $result['message']
        ]);
    }
} 